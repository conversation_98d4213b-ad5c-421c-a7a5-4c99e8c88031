using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using Yidev.LocalAI.Models;

namespace Yidev.LocalAI.Services
{
    /// <summary>
    /// 用户使用次数跟踪服务
    /// </summary>
    public class UsageTrackingService
    {
        /// <summary>
        /// 记录用户使用
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="usageType">使用类型</param>
        /// <param name="topicId">相关话题ID（可选）</param>
        /// <returns>是否记录成功</returns>
        public async Task<bool> RecordUsageAsync(string username, string usageType, int? topicId = null)
        {
            try
            {
                using var context = new DatabaseContext();
                
                var usageRecord = UserUsageRecord.Create(username, usageType, topicId);
                context.UserUsageRecords.Add(usageRecord);
                await context.SaveChangesAsync();
                
                Log.Information("记录用户使用: {Username}, {UsageType}, TopicId: {TopicId}", 
                    username, usageType, topicId);
                
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "记录用户使用失败: {Username}, {UsageType}", username, usageType);
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否超出使用限制
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="usageType">使用类型</param>
        /// <returns>检查结果</returns>
        public async Task<UsageLimitCheckResult> CheckUsageLimitAsync(string username, string usageType)
        {
            try
            {
                using var context = new DatabaseContext();
                
                // 获取用户限制配置
                var limitConfig = await GetOrCreateUserLimitConfigAsync(context, username);
                
                if (!limitConfig.IsEnabled)
                {
                    return new UsageLimitCheckResult
                    {
                        IsAllowed = true,
                        Message = "使用限制已禁用"
                    };
                }
                
                // 获取当前月份的使用次数
                var now = DateTime.Now;
                var currentUsage = await context.UserUsageRecords
                    .Where(u => u.Username == username && 
                               u.UsageType == usageType && 
                               u.Year == now.Year && 
                               u.Month == now.Month)
                    .CountAsync();
                
                var limit = limitConfig.GetLimitForUsageType(usageType);
                var isAllowed = currentUsage < limit;
                
                var result = new UsageLimitCheckResult
                {
                    IsAllowed = isAllowed,
                    CurrentUsage = currentUsage,
                    Limit = limit,
                    RemainingUsage = Math.Max(0, limit - currentUsage),
                    Message = isAllowed ? 
                        $"剩余使用次数: {limit - currentUsage}" : 
                        $"已达到月度使用限制 ({currentUsage}/{limit})"
                };
                
                Log.Information("用户使用限制检查: {Username}, {UsageType}, 当前使用: {CurrentUsage}/{Limit}, 允许: {IsAllowed}", 
                    username, usageType, currentUsage, limit, isAllowed);
                
                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "检查用户使用限制失败: {Username}, {UsageType}", username, usageType);
                return new UsageLimitCheckResult
                {
                    IsAllowed = false,
                    Message = "检查使用限制时发生错误"
                };
            }
        }

        /// <summary>
        /// 获取用户当前月份的使用统计
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>使用统计</returns>
        public async Task<UserUsageStatistics> GetUserUsageStatisticsAsync(string username)
        {
            try
            {
                using var context = new DatabaseContext();
                
                var limitConfig = await GetOrCreateUserLimitConfigAsync(context, username);
                var now = DateTime.Now;
                
                var chatUsage = await context.UserUsageRecords
                    .Where(u => u.Username == username && 
                               u.UsageType == UsageTypes.ChatCompletion && 
                               u.Year == now.Year && 
                               u.Month == now.Month)
                    .CountAsync();
                
                var titleUsage = await context.UserUsageRecords
                    .Where(u => u.Username == username && 
                               u.UsageType == UsageTypes.TopicTitleGeneration && 
                               u.Year == now.Year && 
                               u.Month == now.Month)
                    .CountAsync();
                
                return new UserUsageStatistics
                {
                    Username = username,
                    Year = now.Year,
                    Month = now.Month,
                    ChatUsage = chatUsage,
                    ChatLimit = limitConfig.MonthlyChatLimit,
                    TitleGenerationUsage = titleUsage,
                    TitleGenerationLimit = limitConfig.MonthlyTitleGenerationLimit,
                    IsLimitEnabled = limitConfig.IsEnabled
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, "获取用户使用统计失败: {Username}", username);
                return new UserUsageStatistics
                {
                    Username = username,
                    Year = DateTime.Now.Year,
                    Month = DateTime.Now.Month
                };
            }
        }

        /// <summary>
        /// 获取或创建用户限制配置
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="username">用户名</param>
        /// <returns>用户限制配置</returns>
        private async Task<UserLimitConfig> GetOrCreateUserLimitConfigAsync(DatabaseContext context, string username)
        {
            var config = await context.UserLimitConfigs
                .FirstOrDefaultAsync(c => c.Username == username);
            
            if (config == null)
            {
                config = UserLimitConfig.CreateDefault(username);
                context.UserLimitConfigs.Add(config);
                await context.SaveChangesAsync();
                
                Log.Information("为用户创建默认限制配置: {Username}", username);
            }
            
            return config;
        }

        /// <summary>
        /// 更新用户限制配置
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="chatLimit">聊天限制</param>
        /// <param name="titleLimit">标题生成限制</param>
        /// <param name="isEnabled">是否启用限制</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateUserLimitConfigAsync(string username, int chatLimit, int titleLimit, bool isEnabled)
        {
            try
            {
                using var context = new DatabaseContext();
                
                var config = await GetOrCreateUserLimitConfigAsync(context, username);
                config.MonthlyChatLimit = chatLimit;
                config.MonthlyTitleGenerationLimit = titleLimit;
                config.IsEnabled = isEnabled;
                config.UpdateTimestamp();
                
                await context.SaveChangesAsync();
                
                Log.Information("更新用户限制配置: {Username}, Chat: {ChatLimit}, Title: {TitleLimit}, Enabled: {IsEnabled}", 
                    username, chatLimit, titleLimit, isEnabled);
                
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "更新用户限制配置失败: {Username}", username);
                return false;
            }
        }
    }

    /// <summary>
    /// 使用限制检查结果
    /// </summary>
    public class UsageLimitCheckResult
    {
        public bool IsAllowed { get; set; }
        public int CurrentUsage { get; set; }
        public int Limit { get; set; }
        public int RemainingUsage { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// 用户使用统计
    /// </summary>
    public class UserUsageStatistics
    {
        public string Username { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public int ChatUsage { get; set; }
        public int ChatLimit { get; set; }
        public int TitleGenerationUsage { get; set; }
        public int TitleGenerationLimit { get; set; }
        public bool IsLimitEnabled { get; set; }
        
        public int RemainingChatUsage => Math.Max(0, ChatLimit - ChatUsage);
        public int RemainingTitleGenerationUsage => Math.Max(0, TitleGenerationLimit - TitleGenerationUsage);
    }
}
