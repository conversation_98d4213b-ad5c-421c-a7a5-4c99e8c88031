<Window x:Class="Yidev.LocalAI.SplashWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Yidev LocalAI" Height="300" Width="500"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Topmost="True">
    
    <Border Background="#FF2D2D30" CornerRadius="10" BorderBrush="#FF3F3F46" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.3"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="50"/>
            </Grid.RowDefinitions>
            
            <!-- Logo 区域 -->
            <StackPanel Grid.Row="0" VerticalAlignment="Center" HorizontalAlignment="Center">
                <Image Source="Yidev.png" Width="80" Height="80" Margin="0,20,0,10"/>
                <TextBlock Text="Yidev LocalAI" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"/>
                <TextBlock Text="智能对话助手" 
                          FontSize="14" 
                          Foreground="#FFCCCCCC" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
            
            <!-- 加载状态 -->
            <TextBlock x:Name="StatusText" 
                      Grid.Row="1"
                      Text="正在初始化..." 
                      FontSize="12" 
                      Foreground="#FFAAAAAA" 
                      HorizontalAlignment="Center"
                      Margin="0,10,0,5"/>
            
            <!-- 进度条 -->
            <ProgressBar x:Name="ProgressBar" 
                        Grid.Row="2"
                        Height="4" 
                        Margin="50,0,50,0"
                        Background="#FF404040"
                        Foreground="#FF007ACC"
                        BorderThickness="0"
                        IsIndeterminate="True"/>
            
            <!-- 版本信息 -->
            <TextBlock Grid.Row="3" 
                      Text="版本 1.0.0" 
                      FontSize="10" 
                      Foreground="#FF666666" 
                      HorizontalAlignment="Center"
                      VerticalAlignment="Bottom"
                      Margin="0,0,0,10"/>
        </Grid>
    </Border>
</Window>
