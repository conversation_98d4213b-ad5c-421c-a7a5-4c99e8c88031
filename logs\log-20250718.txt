2025-07-18 16:54:18.502 +08:00 [INF] 依赖注入容器构建完成，耗时: 138ms
2025-07-18 16:54:18.566 +08:00 [DBG] Hosting starting
2025-07-18 16:54:18.583 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 16:54:18.589 +08:00 [INF] Hosting environment: Production
2025-07-18 16:54:18.590 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-18 16:54:18.591 +08:00 [DBG] Hosting started
2025-07-18 16:54:18.592 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-18 16:54:18.781 +08:00 [INF] TopicService initialized.
2025-07-18 16:54:18.783 +08:00 [INF] 主窗口创建完成，耗时: 189ms
2025-07-18 16:54:19.537 +08:00 [DBG] warn: 2025/7/18 16:54:19.537 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 16:54:19.710 +08:00 [DBG] info: 2025/7/18 16:54:19.710 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 16:54:19.716 +08:00 [INF] Database ensured and initialized.
2025-07-18 16:54:19.720 +08:00 [INF] Getting topics for user: llk
2025-07-18 16:54:20.252 +08:00 [DBG] info: 2025/7/18 16:54:20.252 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 16:54:20.318 +08:00 [INF] Getting messages for topic ID: 1
2025-07-18 16:54:20.397 +08:00 [DBG] info: 2025/7/18 16:54:20.397 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 16:54:20.424 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 16:54:20.440 +08:00 [DBG] fail: 2025/7/18 16:54:20.440 RelationalEventId.CommandError[20102] (Microsoft.EntityFrameworkCore.Database.Command) 
      Failed executing DbCommand (3ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "u"."Id", "u"."CreatedAt", "u"."IsEnabled", "u"."MonthlyChatLimit", "u"."MonthlyTitleGenerationLimit", "u"."UpdatedAt", "u"."Username"
      FROM "UserLimitConfigs" AS "u"
      WHERE "u"."Username" = @__username_0
      LIMIT 1
2025-07-18 16:54:20.443 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 16:54:20.446 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 16:54:20.454 +08:00 [DBG] fail: 2025/7/18 16:54:20.454 CoreEventId.QueryIterationFailed[10100] (Microsoft.EntityFrameworkCore.Query) 
      An exception occurred while iterating over the results of a query for context type 'Yidev.LocalAI.Services.DatabaseContext'.
      Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: UserLimitConfigs'.
         at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
         at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
         at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
         at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-18 16:54:20.457 +08:00 [ERR] 获取用户使用统计失败: llk
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: UserLimitConfigs'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.UsageTrackingService.GetOrCreateUserLimitConfigAsync(DatabaseContext context, String username) in D:\yidev\Source\yidev.localai\Services\UsageTrackingService.cs:line 167
   at Yidev.LocalAI.Services.UsageTrackingService.GetUserUsageStatisticsAsync(String username) in D:\yidev\Source\yidev.localai\Services\UsageTrackingService.cs:line 118
2025-07-18 16:54:20.471 +08:00 [INF] 已加载用户使用统计: llk, 聊天: 0/0, 标题: 0/0
2025-07-18 16:54:20.588 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 16:54:20.590 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 16:54:20.591 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-18 16:54:20.753 +08:00 [INF] 主窗口显示完成，耗时: 1968ms
2025-07-18 16:54:20.754 +08:00 [INF] 应用程序启动完成，总耗时: 2807ms
2025-07-18 16:54:23.804 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 16:54:29.729 +08:00 [ERR] 在后台初始化 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeMcpPluginsAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 81
2025-07-18 16:54:29.739 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-18 16:56:17.000 +08:00 [INF] Application Shutting Down
2025-07-18 16:56:17.003 +08:00 [DBG] Hosting stopping
2025-07-18 16:56:17.005 +08:00 [INF] Application is shutting down...
2025-07-18 16:56:17.006 +08:00 [DBG] Hosting stopped
