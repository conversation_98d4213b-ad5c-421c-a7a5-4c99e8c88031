{"format": 1, "restore": {"D:\\yidev\\Source\\yidev.localai\\Yidev.LocalAI.csproj": {}}, "projects": {"D:\\yidev\\Source\\yidev.localai\\Yidev.LocalAI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\yidev\\Source\\yidev.localai\\Yidev.LocalAI.csproj", "projectName": "Yidev.LocalAI", "projectPath": "D:\\yidev\\Source\\yidev.localai\\Yidev.LocalAI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\yidev\\Source\\yidev.localai\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows10.0.26100.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://proget.yidev.cn/nuget/devpack/": {}}, "frameworks": {"net9.0-windows10.0.26100": {"targetAlias": "net9.0-windows10.0.26100.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0-windows10.0.26100": {"targetAlias": "net9.0-windows10.0.26100.0", "dependencies": {"Markdig.Wpf": {"target": "Package", "version": "[*******, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.57.0, )"}, "Microsoft.SemanticKernel.Connectors.OpenAI": {"target": "Package", "version": "[1.57.0, )"}, "Microsoft.SemanticKernel.Plugins.Memory": {"target": "Package", "version": "[1.57.0-alpha, )"}, "ModelContextProtocol-SemanticKernel": {"target": "Package", "version": "[0.3.0-preview-01, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Debug": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "Yidev.Agent.Oidc": {"target": "Package", "version": "[1.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.26100.54, 10.0.26100.54]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}