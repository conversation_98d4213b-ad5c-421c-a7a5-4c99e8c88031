2025-07-18 10:29:53.896 +08:00 [INF] Application Shutting Down
2025-07-18 10:29:53.903 +08:00 [DBG] Hosting stopping
2025-07-18 10:29:53.905 +08:00 [INF] Application is shutting down...
2025-07-18 10:29:53.910 +08:00 [DBG] Hosting stopped
2025-07-18 10:32:30.604 +08:00 [INF] 依赖注入容器构建完成，耗时: 3187ms
2025-07-18 10:32:30.687 +08:00 [DBG] Hosting starting
2025-07-18 10:32:30.705 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 10:32:30.710 +08:00 [INF] Hosting environment: Production
2025-07-18 10:32:30.711 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 10:32:30.712 +08:00 [DBG] Hosting started
2025-07-18 10:32:30.713 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-18 10:32:31.218 +08:00 [INF] TopicService initialized.
2025-07-18 10:32:31.220 +08:00 [INF] 主窗口创建完成，耗时: 506ms
2025-07-18 10:32:34.128 +08:00 [DBG] warn: 2025/7/18 10:32:34.128 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 10:32:34.312 +08:00 [DBG] info: 2025/7/18 10:32:34.312 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (27ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 10:32:34.318 +08:00 [INF] Database ensured and initialized.
2025-07-18 10:32:34.322 +08:00 [INF] Getting topics for user: llk
2025-07-18 10:32:34.832 +08:00 [DBG] info: 2025/7/18 10:32:34.832 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 10:32:34.895 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 10:32:34.972 +08:00 [DBG] info: 2025/7/18 10:32:34.972 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 10:32:35.458 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 10:32:35.796 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 10:32:35.877 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 10:32:36.489 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 10:32:36.491 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 10:32:36.492 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 10:32:36.528 +08:00 [INF] 主窗口显示完成，耗时: 5306ms
2025-07-18 10:32:36.529 +08:00 [INF] 应用程序启动完成，总耗时: 9516ms
2025-07-18 10:32:41.452 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 10:32:45.598 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 10:32:45.740 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 10:32:45.742 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 10:43:02.532 +08:00 [INF] Application Shutting Down
2025-07-18 10:43:02.536 +08:00 [DBG] Hosting stopping
2025-07-18 10:43:02.538 +08:00 [INF] Application is shutting down...
2025-07-18 10:43:02.545 +08:00 [DBG] Hosting stopped
2025-07-18 10:44:05.121 +08:00 [INF] 依赖注入容器构建完成，耗时: 130ms
2025-07-18 10:44:05.183 +08:00 [DBG] Hosting starting
2025-07-18 10:44:05.200 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 10:44:05.205 +08:00 [INF] Hosting environment: Production
2025-07-18 10:44:05.206 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 10:44:05.208 +08:00 [DBG] Hosting started
2025-07-18 10:44:05.208 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-18 10:44:05.387 +08:00 [INF] TopicService initialized.
2025-07-18 10:44:05.389 +08:00 [INF] 主窗口创建完成，耗时: 179ms
2025-07-18 10:44:06.034 +08:00 [DBG] warn: 2025/7/18 10:44:06.034 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 10:44:06.191 +08:00 [DBG] info: 2025/7/18 10:44:06.191 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 10:44:06.196 +08:00 [INF] Database ensured and initialized.
2025-07-18 10:44:06.200 +08:00 [INF] Getting topics for user: llk
2025-07-18 10:44:06.731 +08:00 [DBG] info: 2025/7/18 10:44:06.731 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 10:44:06.798 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 10:44:06.879 +08:00 [DBG] info: 2025/7/18 10:44:06.879 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 10:44:06.908 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 10:44:06.933 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 10:44:06.936 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 10:44:07.080 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 10:44:07.082 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 10:44:07.083 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 10:44:07.325 +08:00 [INF] 主窗口显示完成，耗时: 1934ms
2025-07-18 10:44:07.328 +08:00 [INF] 应用程序启动完成，总耗时: 2737ms
2025-07-18 10:44:10.609 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 10:44:14.815 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 10:44:14.895 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 10:44:14.896 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 10:44:50.563 +08:00 [INF] Application Shutting Down
2025-07-18 10:44:50.569 +08:00 [DBG] Hosting stopping
2025-07-18 10:44:50.570 +08:00 [INF] Application is shutting down...
2025-07-18 10:44:50.578 +08:00 [DBG] Hosting stopped
2025-07-18 11:10:50.728 +08:00 [INF] 依赖注入容器构建完成，耗时: 136ms
2025-07-18 11:10:50.799 +08:00 [DBG] Hosting starting
2025-07-18 11:10:50.817 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:10:50.823 +08:00 [INF] Hosting environment: Production
2025-07-18 11:10:50.824 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:10:50.825 +08:00 [DBG] Hosting started
2025-07-18 11:10:50.826 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-18 11:10:51.005 +08:00 [INF] TopicService initialized.
2025-07-18 11:10:51.007 +08:00 [INF] 主窗口创建完成，耗时: 180ms
2025-07-18 11:10:51.672 +08:00 [DBG] warn: 2025/7/18 11:10:51.671 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:10:51.839 +08:00 [DBG] info: 2025/7/18 11:10:51.839 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (20ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:10:51.845 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:10:51.849 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:10:52.381 +08:00 [DBG] info: 2025/7/18 11:10:52.381 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:10:52.444 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:10:52.524 +08:00 [DBG] info: 2025/7/18 11:10:52.524 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:10:52.552 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:10:52.573 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:10:52.576 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:10:52.716 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:10:52.718 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:10:52.719 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:10:52.925 +08:00 [INF] 主窗口显示完成，耗时: 1916ms
2025-07-18 11:10:52.927 +08:00 [INF] 应用程序启动完成，总耗时: 2756ms
2025-07-18 11:10:56.792 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:11:00.764 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:11:00.844 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:11:00.845 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:14:53.609 +08:00 [INF] Getting messages for topic ID: 35
2025-07-18 11:14:53.616 +08:00 [DBG] info: 2025/7/18 11:14:53.616 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:14:53.620 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:14:53.621 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:14:59.199 +08:00 [INF] Getting messages for topic ID: 36
2025-07-18 11:14:59.202 +08:00 [DBG] info: 2025/7/18 11:14:59.202 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='36'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:14:59.206 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:14:59.207 +08:00 [INF] Setting conversation history. Message count: 3
2025-07-18 11:15:01.466 +08:00 [INF] Getting messages for topic ID: 37
2025-07-18 11:15:01.469 +08:00 [DBG] info: 2025/7/18 11:15:01.469 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='37'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:01.472 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:01.473 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:15:03.207 +08:00 [INF] Getting messages for topic ID: 38
2025-07-18 11:15:03.209 +08:00 [DBG] info: 2025/7/18 11:15:03.209 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='38'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:03.213 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:03.214 +08:00 [INF] Setting conversation history. Message count: 8
2025-07-18 11:15:08.619 +08:00 [INF] Getting messages for topic ID: 39
2025-07-18 11:15:08.622 +08:00 [DBG] info: 2025/7/18 11:15:08.622 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='39'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:08.625 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:08.626 +08:00 [INF] Setting conversation history. Message count: 6
2025-07-18 11:15:14.402 +08:00 [INF] Getting messages for topic ID: 40
2025-07-18 11:15:14.404 +08:00 [DBG] info: 2025/7/18 11:15:14.404 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='40'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:14.407 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:14.408 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:15:16.279 +08:00 [INF] Getting messages for topic ID: 41
2025-07-18 11:15:16.281 +08:00 [DBG] info: 2025/7/18 11:15:16.281 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:16.285 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:16.286 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-18 11:15:17.835 +08:00 [INF] Getting messages for topic ID: 42
2025-07-18 11:15:17.837 +08:00 [DBG] info: 2025/7/18 11:15:17.837 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:17.840 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:17.841 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:15:23.907 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:15:23.910 +08:00 [DBG] info: 2025/7/18 11:15:23.910 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:15:23.912 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:15:23.913 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:15:58.490 +08:00 [INF] Application Shutting Down
2025-07-18 11:15:58.495 +08:00 [DBG] Hosting stopping
2025-07-18 11:15:58.496 +08:00 [INF] Application is shutting down...
2025-07-18 11:15:58.499 +08:00 [DBG] Hosting stopped
2025-07-18 11:16:05.175 +08:00 [INF] 依赖注入容器构建完成，耗时: 128ms
2025-07-18 11:16:05.236 +08:00 [DBG] Hosting starting
2025-07-18 11:16:05.253 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:16:05.259 +08:00 [INF] Hosting environment: Production
2025-07-18 11:16:05.260 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:16:05.262 +08:00 [DBG] Hosting started
2025-07-18 11:16:05.263 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-18 11:16:05.456 +08:00 [INF] TopicService initialized.
2025-07-18 11:16:05.458 +08:00 [INF] 主窗口创建完成，耗时: 194ms
2025-07-18 11:16:06.119 +08:00 [DBG] warn: 2025/7/18 11:16:06.119 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:16:06.277 +08:00 [DBG] info: 2025/7/18 11:16:06.277 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:16:06.282 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:16:06.286 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:16:06.824 +08:00 [DBG] info: 2025/7/18 11:16:06.824 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:16:06.886 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:16:06.966 +08:00 [DBG] info: 2025/7/18 11:16:06.966 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:16:06.989 +08:00 [ERR] aa D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\search_results.png
2025-07-18 11:16:06.995 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:16:07.015 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:16:07.018 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:16:07.158 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:16:07.160 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:16:07.161 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:16:07.412 +08:00 [INF] 主窗口显示完成，耗时: 1952ms
2025-07-18 11:16:07.414 +08:00 [INF] 应用程序启动完成，总耗时: 2777ms
2025-07-18 11:16:11.586 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:16:15.852 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:16:15.936 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:16:15.938 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:21:06.747 +08:00 [INF] Application Shutting Down
2025-07-18 11:21:06.751 +08:00 [DBG] Hosting stopping
2025-07-18 11:21:06.752 +08:00 [INF] Application is shutting down...
2025-07-18 11:21:06.754 +08:00 [DBG] Hosting stopped
2025-07-18 11:21:12.563 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-18 11:21:12.624 +08:00 [DBG] Hosting starting
2025-07-18 11:21:12.640 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:21:12.646 +08:00 [INF] Hosting environment: Production
2025-07-18 11:21:12.648 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:21:12.649 +08:00 [DBG] Hosting started
2025-07-18 11:21:12.650 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-18 11:21:12.834 +08:00 [INF] TopicService initialized.
2025-07-18 11:21:12.837 +08:00 [INF] 主窗口创建完成，耗时: 185ms
2025-07-18 11:21:13.514 +08:00 [DBG] warn: 2025/7/18 11:21:13.513 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:21:13.674 +08:00 [DBG] info: 2025/7/18 11:21:13.674 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:21:13.680 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:21:13.684 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:21:14.209 +08:00 [DBG] info: 2025/7/18 11:21:14.209 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:21:14.273 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:21:14.351 +08:00 [DBG] info: 2025/7/18 11:21:14.351 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:21:14.375 +08:00 [ERR] aa D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\uploads\llk\search_results.png
2025-07-18 11:21:14.382 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:21:14.402 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:21:14.405 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:21:14.549 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:21:14.552 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:21:14.553 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:21:14.834 +08:00 [INF] 主窗口显示完成，耗时: 1995ms
2025-07-18 11:21:14.836 +08:00 [INF] 应用程序启动完成，总耗时: 2806ms
2025-07-18 11:21:17.795 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:21:22.506 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:21:22.590 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:21:22.591 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:21:40.761 +08:00 [INF] Application Shutting Down
2025-07-18 11:21:40.765 +08:00 [DBG] Hosting stopping
2025-07-18 11:21:40.767 +08:00 [INF] Application is shutting down...
2025-07-18 11:21:40.769 +08:00 [DBG] Hosting stopped
2025-07-18 11:21:55.103 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-18 11:21:55.164 +08:00 [DBG] Hosting starting
2025-07-18 11:21:55.181 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:21:55.187 +08:00 [INF] Hosting environment: Production
2025-07-18 11:21:55.188 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:21:55.189 +08:00 [DBG] Hosting started
2025-07-18 11:21:55.190 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-18 11:21:55.375 +08:00 [INF] TopicService initialized.
2025-07-18 11:21:55.377 +08:00 [INF] 主窗口创建完成，耗时: 186ms
2025-07-18 11:21:56.048 +08:00 [DBG] warn: 2025/7/18 11:21:56.048 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:21:56.208 +08:00 [DBG] info: 2025/7/18 11:21:56.208 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:21:56.213 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:21:56.217 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:21:56.748 +08:00 [DBG] info: 2025/7/18 11:21:56.748 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:21:56.811 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:21:56.892 +08:00 [DBG] info: 2025/7/18 11:21:56.892 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:21:56.922 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:21:56.942 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:21:56.946 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:21:57.090 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:21:57.093 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:21:57.094 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:21:57.352 +08:00 [INF] 主窗口显示完成，耗时: 1973ms
2025-07-18 11:21:57.354 +08:00 [INF] 应用程序启动完成，总耗时: 2774ms
2025-07-18 11:22:01.268 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:22:05.288 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:22:05.369 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:22:05.370 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:22:17.234 +08:00 [INF] Deleting message ID: 233
2025-07-18 11:22:17.333 +08:00 [DBG] info: 2025/7/18 11:22:17.333 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='233'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-18 11:22:17.351 +08:00 [INF] Getting chat response for user message: 搜索 祎开发 并截图保存
2025-07-18 11:22:17.378 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-18 11:22:17.380 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-18 11:22:54.253 +08:00 [INF] Received chat response: 好的，已为您搜索“祎开发”并截图。

不过，搜索结果被 reCAPTCHA（人机验证）拦截了，所以我截取的是当前的验证页面。

图片已保存到您的根目录下的 search_results.png 文件中。

需要我尝试点击“我不是机器人”来继续吗？
2025-07-18 11:22:54.257 +08:00 [INF] Adding message to topic ID: 43
2025-07-18 11:22:54.275 +08:00 [DBG] info: 2025/7/18 11:22:54.275 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，已为您搜索“祎开发”并截图。

不过，搜索结果被 reCAPTCHA（人机验证）拦截了，所以我截取的是当前的验证页面。

图片已保存到您的根目录下的 search_results.png 文件中。

需要我尝试点击“我不是机器人”来继续吗？' (Nullable = false) (Size = 123), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-18T11:22:54.2564257+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-18 11:24:23.334 +08:00 [INF] Updating message ID: 232
2025-07-18 11:24:23.340 +08:00 [DBG] info: 2025/7/18 11:24:23.340 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p5='232', @p0='用百度搜索 祎开发 并截图保存' (Nullable = false) (Size = 15), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T17:22:03.5470220' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      UPDATE "ChatMessages" SET "Content" = @p0, "Role" = @p1, "SenderName" = @p2, "Timestamp" = @p3, "TopicId" = @p4
      WHERE "Id" = @p5
      RETURNING 1;
2025-07-18 11:24:23.460 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:24:23.465 +08:00 [DBG] info: 2025/7/18 11:24:23.465 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:24:23.467 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:24:23.469 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:24:35.230 +08:00 [INF] Deleting message ID: 234
2025-07-18 11:24:35.233 +08:00 [DBG] info: 2025/7/18 11:24:35.233 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='234'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-18 11:24:35.250 +08:00 [INF] Getting chat response for user message: 用百度搜索 祎开发 并截图保存
2025-07-18 11:26:35.256 +08:00 [WRN] Chat response request timed out after 2 minutes
2025-07-18 11:26:35.257 +08:00 [INF] Adding message to topic ID: 43
2025-07-18 11:26:35.260 +08:00 [DBG] info: 2025/7/18 11:26:35.260 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='⚠️ AI服务响应超时，请稍后重试

请检查网络连接或稍后重试。' (Nullable = false) (Size = 32), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-18T11:26:35.2576868+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-18 11:26:46.191 +08:00 [INF] Deleting message ID: 235
2025-07-18 11:26:46.194 +08:00 [DBG] info: 2025/7/18 11:26:46.194 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='235'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-18 11:26:46.198 +08:00 [INF] Getting chat response for user message: 用百度搜索 祎开发 并截图保存
2025-07-18 11:28:10.693 +08:00 [INF] Adding message to topic ID: 43
2025-07-18 11:28:10.693 +08:00 [WRN] Chat response request timed out after 2 minutes
2025-07-18 11:28:10.696 +08:00 [DBG] info: 2025/7/18 11:28:10.696 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='⏹️ 请求已被用户取消。' (Nullable = false) (Size = 12), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-18T11:28:10.6923953+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-18 11:28:10.696 +08:00 [INF] Adding message to topic ID: 43
2025-07-18 11:28:10.865 +08:00 [DBG] info: 2025/7/18 11:28:10.865 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (163ms) [Parameters=[@p0='⚠️ AI服务响应超时，请稍后重试

请检查网络连接或稍后重试。' (Nullable = false) (Size = 32), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-18T11:28:10.6967172+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-18 11:28:13.779 +08:00 [INF] Application Shutting Down
2025-07-18 11:28:13.783 +08:00 [DBG] Hosting stopping
2025-07-18 11:28:13.785 +08:00 [INF] Application is shutting down...
2025-07-18 11:28:13.789 +08:00 [DBG] Hosting stopped
2025-07-18 11:28:18.237 +08:00 [INF] 依赖注入容器构建完成，耗时: 130ms
2025-07-18 11:28:18.298 +08:00 [DBG] Hosting starting
2025-07-18 11:28:18.315 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:28:18.320 +08:00 [INF] Hosting environment: Production
2025-07-18 11:28:18.321 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:28:18.322 +08:00 [DBG] Hosting started
2025-07-18 11:28:18.323 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-18 11:28:18.502 +08:00 [INF] TopicService initialized.
2025-07-18 11:28:18.504 +08:00 [INF] 主窗口创建完成，耗时: 180ms
2025-07-18 11:28:19.157 +08:00 [DBG] warn: 2025/7/18 11:28:19.157 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:28:19.318 +08:00 [DBG] info: 2025/7/18 11:28:19.318 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:28:19.324 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:28:19.327 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:28:19.863 +08:00 [DBG] info: 2025/7/18 11:28:19.863 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:28:19.928 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:28:20.011 +08:00 [DBG] info: 2025/7/18 11:28:20.011 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:28:20.042 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:28:20.064 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:28:20.067 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:28:20.206 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:28:20.209 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:28:20.210 +08:00 [INF] Setting conversation history. Message count: 3
2025-07-18 11:28:20.466 +08:00 [INF] 主窗口显示完成，耗时: 1960ms
2025-07-18 11:28:20.468 +08:00 [INF] 应用程序启动完成，总耗时: 2765ms
2025-07-18 11:28:24.347 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:28:28.836 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:28:28.918 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:28:28.919 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:29:08.140 +08:00 [INF] Deleting message ID: 237
2025-07-18 11:29:08.240 +08:00 [DBG] info: 2025/7/18 11:29:08.240 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='237'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-18 11:29:13.589 +08:00 [INF] Deleting message ID: 236
2025-07-18 11:29:13.593 +08:00 [DBG] info: 2025/7/18 11:29:13.593 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='236'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-18 11:29:13.607 +08:00 [INF] Getting chat response for user message: 用百度搜索 祎开发 并截图保存
2025-07-18 11:29:13.632 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-18 11:29:13.633 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-18 11:30:32.660 +08:00 [INF] Received chat response: 好的，已为您在百度上搜索“祎开发”并截图保存。截图文件名为 `yikaifa_screenshot.png`。
2025-07-18 11:30:32.663 +08:00 [INF] Adding message to topic ID: 43
2025-07-18 11:30:32.680 +08:00 [DBG] info: 2025/7/18 11:30:32.680 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，已为您在百度上搜索“祎开发”并截图保存。截图文件名为 `yikaifa_screenshot.png`。' (Nullable = false) (Size = 55), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-18T11:30:32.6625806+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-18 11:31:03.714 +08:00 [INF] Getting messages for topic ID: 42
2025-07-18 11:31:03.719 +08:00 [DBG] info: 2025/7/18 11:31:03.719 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:31:03.730 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:31:03.733 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:31:09.135 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:31:09.138 +08:00 [DBG] info: 2025/7/18 11:31:09.138 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:31:09.140 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:31:09.141 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:32:17.713 +08:00 [INF] Application Shutting Down
2025-07-18 11:32:17.717 +08:00 [DBG] Hosting stopping
2025-07-18 11:32:17.718 +08:00 [INF] Application is shutting down...
2025-07-18 11:32:17.721 +08:00 [DBG] Hosting stopped
2025-07-18 11:32:28.923 +08:00 [INF] 依赖注入容器构建完成，耗时: 128ms
2025-07-18 11:32:28.986 +08:00 [DBG] Hosting starting
2025-07-18 11:32:29.003 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:32:29.009 +08:00 [INF] Hosting environment: Production
2025-07-18 11:32:29.010 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:32:29.011 +08:00 [DBG] Hosting started
2025-07-18 11:32:29.012 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-18 11:32:29.196 +08:00 [INF] TopicService initialized.
2025-07-18 11:32:29.198 +08:00 [INF] 主窗口创建完成，耗时: 184ms
2025-07-18 11:32:29.857 +08:00 [DBG] warn: 2025/7/18 11:32:29.857 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:32:30.016 +08:00 [DBG] info: 2025/7/18 11:32:30.016 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:32:30.021 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:32:30.025 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:32:30.577 +08:00 [DBG] info: 2025/7/18 11:32:30.577 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:32:30.641 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:32:30.723 +08:00 [DBG] info: 2025/7/18 11:32:30.723 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:32:30.752 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:32:30.771 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:32:30.774 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:32:30.912 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:32:30.915 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:32:30.916 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:32:31.155 +08:00 [INF] 主窗口显示完成，耗时: 1955ms
2025-07-18 11:32:31.156 +08:00 [INF] 应用程序启动完成，总耗时: 2749ms
2025-07-18 11:32:34.102 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:32:39.205 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:32:39.281 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:32:39.282 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:33:55.166 +08:00 [INF] Application Shutting Down
2025-07-18 11:33:55.170 +08:00 [DBG] Hosting stopping
2025-07-18 11:33:55.172 +08:00 [INF] Application is shutting down...
2025-07-18 11:33:55.179 +08:00 [DBG] Hosting stopped
2025-07-18 11:34:01.377 +08:00 [INF] 依赖注入容器构建完成，耗时: 127ms
2025-07-18 11:34:01.440 +08:00 [DBG] Hosting starting
2025-07-18 11:34:01.456 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:34:01.462 +08:00 [INF] Hosting environment: Production
2025-07-18 11:34:01.463 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:34:01.464 +08:00 [DBG] Hosting started
2025-07-18 11:34:01.465 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-18 11:34:01.657 +08:00 [INF] TopicService initialized.
2025-07-18 11:34:01.659 +08:00 [INF] 主窗口创建完成，耗时: 192ms
2025-07-18 11:34:02.322 +08:00 [DBG] warn: 2025/7/18 11:34:02.321 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:34:02.489 +08:00 [DBG] info: 2025/7/18 11:34:02.489 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:34:02.495 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:34:02.498 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:34:03.026 +08:00 [DBG] info: 2025/7/18 11:34:03.026 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:34:03.087 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:34:03.169 +08:00 [DBG] info: 2025/7/18 11:34:03.169 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:34:03.196 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:34:03.217 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:34:03.219 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:34:03.358 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:34:03.362 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:34:03.363 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:34:03.599 +08:00 [INF] 主窗口显示完成，耗时: 1938ms
2025-07-18 11:34:03.600 +08:00 [INF] 应用程序启动完成，总耗时: 2736ms
2025-07-18 11:34:06.525 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:34:10.631 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:34:10.708 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:34:10.709 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 11:34:34.268 +08:00 [INF] Application Shutting Down
2025-07-18 11:34:34.273 +08:00 [DBG] Hosting stopping
2025-07-18 11:34:34.276 +08:00 [INF] Application is shutting down...
2025-07-18 11:34:34.279 +08:00 [DBG] Hosting stopped
2025-07-18 11:36:56.967 +08:00 [INF] 依赖注入容器构建完成，耗时: 128ms
2025-07-18 11:36:57.035 +08:00 [DBG] Hosting starting
2025-07-18 11:36:57.053 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 11:36:57.059 +08:00 [INF] Hosting environment: Production
2025-07-18 11:36:57.060 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 11:36:57.062 +08:00 [DBG] Hosting started
2025-07-18 11:36:57.063 +08:00 [INF] Host 启动完成，耗时: 37ms
2025-07-18 11:36:57.246 +08:00 [INF] TopicService initialized.
2025-07-18 11:36:57.248 +08:00 [INF] 主窗口创建完成，耗时: 184ms
2025-07-18 11:36:57.911 +08:00 [DBG] warn: 2025/7/18 11:36:57.911 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 11:36:58.071 +08:00 [DBG] info: 2025/7/18 11:36:58.071 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 11:36:58.077 +08:00 [INF] Database ensured and initialized.
2025-07-18 11:36:58.080 +08:00 [INF] Getting topics for user: llk
2025-07-18 11:36:58.604 +08:00 [DBG] info: 2025/7/18 11:36:58.604 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 11:36:58.665 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 11:36:58.745 +08:00 [DBG] info: 2025/7/18 11:36:58.745 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 11:36:58.773 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 11:36:58.792 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 11:36:58.795 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 11:36:58.932 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 11:36:58.935 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 11:36:58.936 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 11:36:59.202 +08:00 [INF] 主窗口显示完成，耗时: 1952ms
2025-07-18 11:36:59.203 +08:00 [INF] 应用程序启动完成，总耗时: 2755ms
2025-07-18 11:37:02.083 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 11:37:06.033 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 11:37:06.111 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 11:37:06.112 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 15:15:29.040 +08:00 [INF] Application Shutting Down
2025-07-18 15:15:29.044 +08:00 [DBG] Hosting stopping
2025-07-18 15:15:29.046 +08:00 [INF] Application is shutting down...
2025-07-18 15:15:29.053 +08:00 [DBG] Hosting stopped
2025-07-18 15:17:04.144 +08:00 [INF] 依赖注入容器构建完成，耗时: 132ms
2025-07-18 15:17:04.214 +08:00 [DBG] Hosting starting
2025-07-18 15:17:04.231 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:17:04.236 +08:00 [INF] Hosting environment: Production
2025-07-18 15:17:04.237 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 15:17:04.238 +08:00 [DBG] Hosting started
2025-07-18 15:17:04.239 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-18 15:17:04.419 +08:00 [INF] TopicService initialized.
2025-07-18 15:17:04.421 +08:00 [INF] 主窗口创建完成，耗时: 180ms
2025-07-18 15:17:05.091 +08:00 [DBG] warn: 2025/7/18 15:17:05.090 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 15:17:05.248 +08:00 [DBG] info: 2025/7/18 15:17:05.248 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 15:17:05.253 +08:00 [INF] Database ensured and initialized.
2025-07-18 15:17:05.256 +08:00 [INF] Getting topics for user: llk
2025-07-18 15:17:05.779 +08:00 [DBG] info: 2025/7/18 15:17:05.779 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 15:17:05.844 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 15:17:05.928 +08:00 [DBG] info: 2025/7/18 15:17:05.928 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 15:17:05.958 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 15:17:05.978 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 15:17:05.981 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 15:17:06.122 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 15:17:06.124 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 15:17:06.125 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 15:17:06.392 +08:00 [INF] 主窗口显示完成，耗时: 1970ms
2025-07-18 15:17:06.394 +08:00 [INF] 应用程序启动完成，总耗时: 2772ms
2025-07-18 15:17:09.688 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 15:17:15.502 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 15:17:15.578 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 15:17:15.580 +08:00 [INF] MCP 插件后台初始化完成
2025-07-18 15:17:36.094 +08:00 [INF] Getting messages for topic ID: 42
2025-07-18 15:17:36.101 +08:00 [DBG] info: 2025/7/18 15:17:36.101 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 15:17:36.104 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 15:17:36.106 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 15:17:38.452 +08:00 [INF] Getting messages for topic ID: 41
2025-07-18 15:17:38.454 +08:00 [DBG] info: 2025/7/18 15:17:38.454 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 15:17:38.459 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 15:17:38.460 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-18 15:17:40.802 +08:00 [INF] Getting messages for topic ID: 40
2025-07-18 15:17:40.804 +08:00 [DBG] info: 2025/7/18 15:17:40.804 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='40'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 15:17:40.807 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 15:17:40.808 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 16:02:15.605 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 16:02:15.609 +08:00 [DBG] info: 2025/7/18 16:02:15.609 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 16:02:15.612 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 16:02:15.613 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 16:29:01.693 +08:00 [INF] Application Shutting Down
2025-07-18 16:29:01.696 +08:00 [DBG] Hosting stopping
2025-07-18 16:29:01.698 +08:00 [INF] Application is shutting down...
2025-07-18 16:29:01.700 +08:00 [DBG] Hosting stopped
2025-07-18 16:56:38.966 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-18 16:56:39.027 +08:00 [DBG] Hosting starting
2025-07-18 16:56:39.044 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 16:56:39.050 +08:00 [INF] Hosting environment: Production
2025-07-18 16:56:39.052 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-18 16:56:39.053 +08:00 [DBG] Hosting started
2025-07-18 16:56:39.054 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-18 16:56:39.237 +08:00 [INF] TopicService initialized.
2025-07-18 16:56:39.240 +08:00 [INF] 主窗口创建完成，耗时: 184ms
2025-07-18 16:56:39.925 +08:00 [DBG] warn: 2025/7/18 16:56:39.925 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-18 16:56:40.088 +08:00 [DBG] info: 2025/7/18 16:56:40.088 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-18 16:56:40.093 +08:00 [INF] Database ensured and initialized.
2025-07-18 16:56:40.097 +08:00 [INF] Getting topics for user: llk
2025-07-18 16:56:40.627 +08:00 [DBG] info: 2025/7/18 16:56:40.627 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-18 16:56:40.696 +08:00 [INF] Getting messages for topic ID: 43
2025-07-18 16:56:40.780 +08:00 [DBG] info: 2025/7/18 16:56:40.780 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-18 16:56:40.809 +08:00 [INF] Initializing SemanticKernelService...
2025-07-18 16:56:40.826 +08:00 [DBG] fail: 2025/7/18 16:56:40.826 RelationalEventId.CommandError[20102] (Microsoft.EntityFrameworkCore.Database.Command) 
      Failed executing DbCommand (3ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "u"."Id", "u"."CreatedAt", "u"."IsEnabled", "u"."MonthlyChatLimit", "u"."MonthlyTitleGenerationLimit", "u"."UpdatedAt", "u"."Username"
      FROM "UserLimitConfigs" AS "u"
      WHERE "u"."Username" = @__username_0
      LIMIT 1
2025-07-18 16:56:40.829 +08:00 [INF] SemanticKernelService initialized.
2025-07-18 16:56:40.834 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-18 16:56:40.842 +08:00 [DBG] fail: 2025/7/18 16:56:40.842 CoreEventId.QueryIterationFailed[10100] (Microsoft.EntityFrameworkCore.Query) 
      An exception occurred while iterating over the results of a query for context type 'Yidev.LocalAI.Services.DatabaseContext'.
      Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: UserLimitConfigs'.
         at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
         at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
         at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
         at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-18 16:56:40.847 +08:00 [ERR] 获取用户使用统计失败: llk
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: UserLimitConfigs'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.UsageTrackingService.GetOrCreateUserLimitConfigAsync(DatabaseContext context, String username) in D:\yidev\Source\yidev.localai\Services\UsageTrackingService.cs:line 167
   at Yidev.LocalAI.Services.UsageTrackingService.GetUserUsageStatisticsAsync(String username) in D:\yidev\Source\yidev.localai\Services\UsageTrackingService.cs:line 118
2025-07-18 16:56:40.883 +08:00 [INF] 已加载用户使用统计: llk, 聊天: 0/0, 标题: 0/0
2025-07-18 16:56:40.982 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-18 16:56:40.985 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-18 16:56:40.986 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-18 16:56:41.319 +08:00 [INF] 主窗口显示完成，耗时: 2077ms
2025-07-18 16:56:41.321 +08:00 [INF] 应用程序启动完成，总耗时: 2885ms
2025-07-18 16:56:44.702 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-18 16:56:48.728 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-18 16:56:48.810 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-18 16:56:48.811 +08:00 [INF] MCP 插件后台初始化完成
