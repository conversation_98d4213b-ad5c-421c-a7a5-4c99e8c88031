﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows10.0.26100.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="src\**" />
    <EmbeddedResource Remove="src\**" />
    <None Remove="src\**" />
    <Page Remove="src\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Test\TestImagePathProcessing.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="chats.png" />
    <None Remove="favicon.ico" />
    <None Remove="uni-ai.png" />
    <None Remove="Yidev.png" />
  </ItemGroup>

  <ItemGroup>
    <Page Remove="MainWindow - 副本.xaml" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="chats.png" />
    <Resource Include="uni-ai.png" />
    <Resource Include="Yidev.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Markdig.Wpf" Version="*******" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.57.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.OpenAI" Version="1.57.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.Memory" Version="1.57.0-alpha" />
    <PackageReference Include="ModelContextProtocol-SemanticKernel" Version="0.3.0-preview-01" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Yidev.Agent.Oidc" Version="1.0.1" />
  </ItemGroup>

</Project>
