using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace Yidev.LocalAI
{
    public partial class SplashWindow : Window
    {
        private readonly DispatcherTimer _timer;
        private int _progressStep = 0;
        private readonly string[] _loadingMessages = new[]
        {
            "正在初始化应用程序...",
            "正在加载配置...",
            "正在连接服务...",
            "正在准备用户界面...",
            "即将完成..."
        };

        public SplashWindow()
        {
            InitializeComponent();
            
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(800)
            };
            _timer.Tick += Timer_Tick;
        }

        public void StartProgress()
        {
            _timer.Start();
        }

        public void UpdateStatus(string message)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = message;
            });
        }

        public void SetProgress(double value)
        {
            Dispatcher.Invoke(() =>
            {
                ProgressBar.IsIndeterminate = false;
                ProgressBar.Value = value;
                ProgressBar.Maximum = 100;
            });
        }

        public void CompleteProgress()
        {
            Dispatcher.Invoke(() =>
            {
                ProgressBar.IsIndeterminate = false;
                ProgressBar.Value = 100;
                StatusText.Text = "启动完成";
            });
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            if (_progressStep < _loadingMessages.Length)
            {
                StatusText.Text = _loadingMessages[_progressStep];
                _progressStep++;
            }
            else
            {
                _timer.Stop();
            }
        }

        public async Task CloseWithFadeOut()
        {
            await Task.Delay(500); // 显示完成状态一会儿
            
            // 淡出动画
            var fadeOut = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = TimeSpan.FromMilliseconds(300)
            };
            
            fadeOut.Completed += (s, e) => Close();
            BeginAnimation(OpacityProperty, fadeOut);
        }
    }
}
