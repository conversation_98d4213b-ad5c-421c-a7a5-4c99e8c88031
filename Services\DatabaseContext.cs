using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Yidev.LocalAI.Models;

namespace Yidev.LocalAI.Services
{
    public class DatabaseContext : DbContext
    {
        public DbSet<Topic> Topics { get; set; }
        public DbSet<ChatMessage> ChatMessages { get; set; }
        public DbSet<UserUsageRecord> UserUsageRecords { get; set; }
        public DbSet<UserLimitConfig> UserLimitConfigs { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlite("Data Source=localai.db")
                .LogTo(message => Log.Debug(message), Microsoft.Extensions.Logging.LogLevel.Information)
                .EnableSensitiveDataLogging();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置UserUsageRecord索引以提高查询性能
            modelBuilder.Entity<UserUsageRecord>()
                .HasIndex(u => new { u.Username, u.Year, u.Month, u.UsageType })
                .HasDatabaseName("IX_UserUsageRecord_Username_Year_Month_UsageType");

            // 配置UserLimitConfig唯一约束
            modelBuilder.Entity<UserLimitConfig>()
                .HasIndex(u => u.Username)
                .IsUnique()
                .HasDatabaseName("IX_UserLimitConfig_Username_Unique");
        }
    }
}
