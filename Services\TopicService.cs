﻿using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Yidev.LocalAI.Models;

namespace Yidev.LocalAI.Services
{
    public class TopicService
    {
        private static readonly object _initLock = new object();
        private static bool _databaseInitialized = false;

        public TopicService()
        {
            // 延迟初始化数据库，不在构造函数中阻塞
            Log.Information("TopicService initialized.");
        }

        private static async Task EnsureDatabaseCreatedAsync()
        {
            if (_databaseInitialized) return;

            lock (_initLock)
            {
                if (_databaseInitialized) return;

                try
                {
                    using var context = new DatabaseContext();
                    context.Database.EnsureCreated();
                    _databaseInitialized = true;
                    Log.Information("Database ensured and initialized.");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Failed to initialize database");
                    throw;
                }
            }
        }

        public async Task<List<Topic>> GetTopicsAsync(string username)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Getting topics for user: {Username}", username);
            using var context = new DatabaseContext();
            return await context.Topics.Where(i => i.UserName == username).OrderByDescending(t => t.CreatedAt).ToListAsync();
        }

        public async Task<Topic> CreateTopicAsync(string name, string username)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Creating topic '{TopicName}' for user: {Username}", name, username);
            using var context = new DatabaseContext();
            var topic = new Topic { Name = name, CreatedAt = DateTime.Now, UserName = username };
            context.Topics.Add(topic);
            await context.SaveChangesAsync();
            Log.Information("Topic '{TopicName}' created with ID: {TopicId}", name, topic.Id);
            return topic;
        }

        public async Task<List<ChatMessage>> GetMessagesByTopicAsync(int topicId)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Getting messages for topic ID: {TopicId}", topicId);
            using var context = new DatabaseContext();
            return await context.ChatMessages
                                 .Where(m => m.TopicId == topicId)
                                 .OrderBy(m => m.Timestamp)
                                 .ToListAsync();
        }

        public async Task AddMessageAsync(ChatMessage message)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Adding message to topic ID: {TopicId}", message.TopicId);
            using var context = new DatabaseContext();
            context.ChatMessages.Add(message);
            await context.SaveChangesAsync();
        }

        public async Task DeleteTopicAsync(Topic topic)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Deleting topic ID: {TopicId}", topic.Id);
            using var context = new DatabaseContext();
            var messages = await context.ChatMessages.Where(m => m.TopicId == topic.Id).ToListAsync();
            context.ChatMessages.RemoveRange(messages);
            context.Topics.Remove(topic);
            await context.SaveChangesAsync();
            Log.Information("Topic ID: {TopicId} deleted.", topic.Id);
        }

        public async Task UpdateMessageAsync(ChatMessage message)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Updating message ID: {MessageId}", message.Id);
            using var context = new DatabaseContext();
            context.ChatMessages.Update(message);
            await context.SaveChangesAsync();
        }

        public async Task DeleteMessageAsync(ChatMessage message)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Deleting message ID: {MessageId}", message.Id);
            using var context = new DatabaseContext();
            context.ChatMessages.Remove(message);
            await context.SaveChangesAsync();
        }

        public async Task UpdateTopicAsync(Topic topic)
        {
            await EnsureDatabaseCreatedAsync();
            Log.Information("Updating topic ID: {TopicId}", topic.Id);
            using var context = new DatabaseContext();
            context.Topics.Update(topic);
            await context.SaveChangesAsync();
        }
    }
}
