using System;
using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;
using System.Diagnostics;
using System.Windows.Threading;
using Yidev.LocalAI.Models;

namespace Yidev.LocalAI
{
    // GridLength动画类
    public class GridLengthAnimation : AnimationTimeline
    {
        public static readonly DependencyProperty FromProperty = DependencyProperty.Register("From", typeof(GridLength?), typeof(GridLengthAnimation));
        public static readonly DependencyProperty ToProperty = DependencyProperty.Register("To", typeof(GridLength?), typeof(GridLengthAnimation));
        public static readonly DependencyProperty EasingFunctionProperty = DependencyProperty.Register("EasingFunction", typeof(IEasingFunction), typeof(GridLengthAnimation));

        // 添加完成事件，使用new关键字避免隐藏Timeline.Completed
        public new event EventHandler? Completed;

        public GridLength? From
        {
            get { return (GridLength?)GetValue(FromProperty); }
            set { SetValue(FromProperty, value); }
        }

        public GridLength? To
        {
            get { return (GridLength?)GetValue(ToProperty); }
            set { SetValue(ToProperty, value); }
        }

        public IEasingFunction EasingFunction
        {
            get { return (IEasingFunction)GetValue(EasingFunctionProperty); }
            set { SetValue(EasingFunctionProperty, value); }
        }

        public override Type TargetPropertyType => typeof(GridLength);

        protected override Freezable CreateInstanceCore()
        {
            return new GridLengthAnimation();
        }

        public override object GetCurrentValue(object defaultOriginValue, object defaultDestinationValue, AnimationClock animationClock)
        {
            if (animationClock.CurrentProgress == null)
                return defaultOriginValue;

            double fromVal = (From ?? (GridLength)defaultOriginValue).Value;
            double toVal = (To ?? (GridLength)defaultDestinationValue).Value;

            double progress = animationClock.CurrentProgress.Value;
            if (EasingFunction != null)
            {
                progress = EasingFunction.Ease(progress);
            }

            // 触发完成事件
            if (animationClock.CurrentProgress.Value >= 1.0 && Completed != null)
            {
                // 使用Application.Current.Dispatcher延迟执行，避免动画过程中修改UI导致异常
                Application.Current.Dispatcher.BeginInvoke(new Action(() => 
                {
                    Completed?.Invoke(this, EventArgs.Empty);
                }));
            }

            double currentValue = fromVal + (toVal - fromVal) * progress;
            return new GridLength(currentValue, GridUnitType.Pixel);
        }
    }

    public partial class MainWindow : Window
    {
        private bool _isSidebarCollapsed = false;
        private double _lastExpandedWidth = 280;
        
        public MainWindow()
        {
            InitializeComponent();
            var viewModel = ViewModelLocator.MainViewModelInstance; // Use shared instance
            DataContext = viewModel;

            // 订阅消息集合的变化事件，以便自动滚动
            if (viewModel.Messages is INotifyCollectionChanged collection)
            {
                collection.CollectionChanged += (s, e) =>
                {
                    // 延迟执行，确保UI已经更新
                    Dispatcher.InvokeAsync(() =>
                    {
                        if (this.FindName("ChatScrollViewer") is ScrollViewer scrollViewer)
                        {
                            scrollViewer.ScrollToEnd();
                        }
                    });
                };
            }

            // 等待控件加载完成后绑定事件
            this.Loaded += MainWindow_Loaded;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is MainViewModel viewModel)
            {
                await viewModel.InitializeAsync();
            }
            // 绑定切换按钮事件
            if (this.FindName("ToggleButton") is Button toggleButton)
            {
                toggleButton.Click -= ToggleButton_Click; // 先移除避免重复绑定
                toggleButton.Click += ToggleButton_Click;
            }

            // 绑定设置按钮事件
            if (this.FindName("SettingsButton") is Button settingsButton)
            {
                settingsButton.Click -= SettingsButton_Click;
                settingsButton.Click += SettingsButton_Click;
            }
        }

        private void ToggleButton_Click(object sender, RoutedEventArgs e)
        {
            ToggleSidebar();
        }

        private void ToggleSidebar()
        {
            try
            {
                var sidebarColumn = this.FindName("SidebarColumn") as ColumnDefinition;
                if (sidebarColumn == null) return;

                // 不使用动画，直接设置宽度，更可靠
                if (_isSidebarCollapsed)
                {
                    // 展开侧边栏
                    sidebarColumn.Width = new GridLength(_lastExpandedWidth > 50 ? _lastExpandedWidth : 280);
                    _isSidebarCollapsed = false;
                    
                    // 更新箭头方向 (指向左)
                    UpdateToggleButtonIcon("M 6 2 L 2 6 L 6 10");
                }
                else
                {
                    // 收缩前记住当前宽度
                    _lastExpandedWidth = sidebarColumn.ActualWidth;
                    
                    // 收缩侧边栏
                    sidebarColumn.Width = new GridLength(0);
                    _isSidebarCollapsed = true;
                    
                    // 更新箭头方向 (指向右)
                    UpdateToggleButtonIcon("M 2 2 L 6 6 L 2 10");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"切换侧边栏时出现错误: {ex.Message}");
            }
        }

        private void UpdateToggleButtonIcon(string pathData)
        {
            if (this.FindName("ToggleButtonIcon") is Path path)
            {
                var geometry = Geometry.Parse(pathData);
                path.Data = geometry;
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new McpSettingsWindow
                {
                    Owner = this
                };
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开设置窗口: {ex.Message}");
            }
        }

        private void Hyperlink_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            try
            {
                var url = e.Parameter as string;
                if (!string.IsNullOrWhiteSpace(url))
                {
                    System.Diagnostics.Debug.WriteLine($"Hyperlink_Executed 被调用，URL: {url}");

                    // 使用系统默认浏览器打开链接
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = url,
                        UseShellExecute = true
                    });

                    System.Diagnostics.Debug.WriteLine($"成功打开链接: {url}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开链接失败: {ex.Message}");

                // 显示错误消息给用户
                MessageBox.Show($"无法打开链接：{e.Parameter}\n\n错误信息：{ex.Message}",
                    "打开链接失败",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }
    }
}
