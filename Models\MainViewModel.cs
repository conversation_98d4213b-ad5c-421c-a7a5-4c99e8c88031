﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Yidev.LocalAI.Services;
using System.Linq;
using Yidev.Agent.Oidc;
using System.Text.Json.Nodes;
using Serilog;
using System.Threading;
using System.Windows;

namespace Yidev.LocalAI.Models
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly TopicService _topicService;
        private readonly UsageTrackingService _usageTrackingService;
        private SemanticKernelService _semanticKernelService;
        private ObservableCollection<ChatMessage> _messages;
        private ObservableCollection<Topic> _topics;
        private string _userInput = string.Empty;
        private bool _isBusy;
        private Topic _selectedTopic;
        private ICommand _sendMessageCommand;
        private ICommand _newTopicCommand;
        private ICommand _deleteTopicCommand;
        private ICommand _editTopicCommand;
        private ICommand _viewImageCommand;
        private ICommand _openLinkCommand;
        private ICommand _cancelCommand;
        private string _userInfo;
        private string _avatar;
        private string _username;
        private CancellationTokenSource _currentRequestCancellation;
        private UserUsageStatistics _usageStatistics;

        public MainViewModel()
        {
            if (AuthenticationService.CurrentAuthenticationData == null)
            {
                Yidev.Agent.Oidc.MainWindow mainWindow = new Yidev.Agent.Oidc.MainWindow();
                mainWindow.ShowDialog();
            }
            else
            {
                _userInfo = AuthenticationService.CurrentAuthenticationData.UserInfoJson;
                var obj = JsonObject.Parse(_userInfo)?.AsObject();
                _username = obj?.AsObject()?["name"].GetValue<string>() ?? string.Empty;
                _avatar = obj?.AsObject()?["avatar"].GetValue<string>() ?? string.Empty;
            }

            _topicService = new TopicService();
            _usageTrackingService = new UsageTrackingService();
            _messages = new ObservableCollection<ChatMessage>();
            _topics = new ObservableCollection<Topic>();
        }

        public async Task InitializeAsync()
        {
            // 延迟初始化 SemanticKernelService，只在需要时创建
            // _semanticKernelService = await SemanticKernelService.CreateAsync();
            await LoadTopics();
            await LoadUsageStatistics();
        }

        /// <summary>
        /// 加载用户使用统计
        /// </summary>
        public async Task LoadUsageStatistics()
        {
            try
            {
                UsageStatistics = await _usageTrackingService.GetUserUsageStatisticsAsync(_username);
                Log.Information("已加载用户使用统计: {Username}, 聊天: {ChatUsage}/{ChatLimit}, 标题: {TitleUsage}/{TitleLimit}",
                    _username, UsageStatistics.ChatUsage, UsageStatistics.ChatLimit,
                    UsageStatistics.TitleGenerationUsage, UsageStatistics.TitleGenerationLimit);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载用户使用统计失败");
            }
        }

        private async Task<SemanticKernelService> GetSemanticKernelServiceAsync()
        {
            if (_semanticKernelService == null)
            {
                _semanticKernelService = await SemanticKernelService.CreateAsync();
            }
            return _semanticKernelService;
        }

        public ObservableCollection<ChatMessage> Messages
        {
            get => _messages;
            set { _messages = value; OnPropertyChanged(); }
        }

        public ObservableCollection<Topic> Topics
        {
            get => _topics;
            set { _topics = value; OnPropertyChanged(); }
        }

        public string UserInput
        {
            get => _userInput;
            set { _userInput = value; OnPropertyChanged(); }
        }

        public string Username
        {
            get => _username;
        }

        public UserUsageStatistics UsageStatistics
        {
            get => _usageStatistics;
            set
            {
                _usageStatistics = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UsageStatusText));
                OnPropertyChanged(nameof(IsUsageLimitReached));
            }
        }

        public string UsageStatusText
        {
            get
            {
                if (UsageStatistics == null || !UsageStatistics.IsLimitEnabled)
                    return "使用限制已禁用";

                return $"本月使用: 聊天 {UsageStatistics.ChatUsage}/{UsageStatistics.ChatLimit}, 标题 {UsageStatistics.TitleGenerationUsage}/{UsageStatistics.TitleGenerationLimit}";
            }
        }

        public bool IsUsageLimitReached
        {
            get
            {
                if (UsageStatistics == null || !UsageStatistics.IsLimitEnabled)
                    return false;

                return UsageStatistics.RemainingChatUsage <= 0;
            }
        }

        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged();
                ((RelayCommand)SendMessageCommand).RaiseCanExecuteChanged();
                if (_cancelCommand != null)
                {
                    ((RelayCommand)_cancelCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public Topic SelectedTopic
        {
            get => _selectedTopic;
            set
            {
                if (_selectedTopic != value)
                {
                    _selectedTopic = value;
                    OnPropertyChanged();
                    LoadMessagesForTopic();
                }
            }
        }

        public ICommand SendMessageCommand => _sendMessageCommand ??= new RelayCommand(SendMessage, () => !IsBusy && !string.IsNullOrWhiteSpace(UserInput) && SelectedTopic != null);
        public ICommand NewTopicCommand => _newTopicCommand ??= new RelayCommand(CreateNewTopic);
        public ICommand DeleteTopicCommand => _deleteTopicCommand ??= new RelayCommand<Topic>(DeleteTopic);
        public ICommand EditTopicCommand => _editTopicCommand ??= new RelayCommand<Topic>(EditTopic);
        public ICommand CopyCommand => new RelayCommand<ChatMessage>(CopyMessage);
        public ICommand EditCommand => new RelayCommand<ChatMessage>(EditMessage);
        public ICommand DeleteMessageCommand => new RelayCommand<ChatMessage>(DeleteMessage);
        public ICommand RegenerateCommand => new RelayCommand<ChatMessage>(RegenerateResponse);
        public ICommand ViewImageCommand => _viewImageCommand ??= new RelayCommand<string>(ViewImage);
        public ICommand OpenLinkCommand => _openLinkCommand ??= new RelayCommand<string>(OpenLink);
        public ICommand CancelCommand => _cancelCommand ??= new RelayCommand(CancelCurrentRequest, () => IsBusy);

        private void CopyMessage(ChatMessage message)
        {
            if (message != null)
            {
                System.Windows.Clipboard.SetText(message.Content);
            }
        }

        private async void EditMessage(ChatMessage message)
        {
            if (message == null || message.Role != "user") return;

            // 简单的实现：用一个输入框来获取新的内容
            var newContent = Microsoft.VisualBasic.Interaction.InputBox("编辑消息:", "编辑", message.Content);
            if (!string.IsNullOrEmpty(newContent) && newContent != message.Content)
            {
                message.Content = newContent;
                await _topicService.UpdateMessageAsync(message);
                LoadMessagesForTopic(); // 重新加载以刷新UI
            }
        }

        private async void DeleteMessage(ChatMessage message)
        {
            if (message == null) return;

            // 显示确认对话框
            var result = MessageBox.Show(
                "确定要删除这条消息吗？此操作无法撤销。",
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question,
                MessageBoxResult.No);

            if (result == MessageBoxResult.Yes)
            {
                await _topicService.DeleteMessageAsync(message);
                Messages.Remove(message);
            }
        }

        private async void RegenerateResponse(ChatMessage message)
        {
            if (message == null || message.Role != "assistant") return;

            var lastUserMessage = Messages.LastOrDefault(m => m.Role == "user" && m.Timestamp < message.Timestamp);
            if (lastUserMessage == null) return;

            IsBusy = true;
            await _topicService.DeleteMessageAsync(message);
            Messages.Remove(message);

            await GenerateAIResponse(lastUserMessage.Content);
            IsBusy = false;
        }

        private void ViewImage(string imageUrl)
        {
            System.Diagnostics.Debug.WriteLine($"ViewImage 被调用，图片URL: {imageUrl}");

            if (string.IsNullOrWhiteSpace(imageUrl))
            {
                System.Diagnostics.Debug.WriteLine("图片URL为空，返回");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine("尝试创建图片查看窗口");
                // 创建图片查看窗口
                var imageViewerWindow = new ImageViewerWindow(imageUrl);
                imageViewerWindow.Show();
                System.Diagnostics.Debug.WriteLine("图片查看窗口已显示");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开图片查看器失败: {ex.Message}");

                // 备用方案：使用系统默认浏览器打开图片
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = imageUrl,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"使用浏览器打开图片失败: {ex2.Message}");
                }
            }
        }

        private void OpenLink(string url)
        {
            System.Diagnostics.Debug.WriteLine($"OpenLink 被调用，URL: {url}");

            if (string.IsNullOrWhiteSpace(url))
            {
                System.Diagnostics.Debug.WriteLine("URL为空，返回");
                return;
            }

            try
            {
                // 使用系统默认浏览器打开链接
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
                System.Diagnostics.Debug.WriteLine($"成功打开链接: {url}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开链接失败: {ex.Message}");

                // 显示错误消息给用户
                System.Windows.MessageBox.Show($"无法打开链接：{url}\n\n错误信息：{ex.Message}",
                    "打开链接失败",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
            }
        }

        private void CancelCurrentRequest()
        {
            if (_currentRequestCancellation != null && !_currentRequestCancellation.Token.IsCancellationRequested)
            {
                _currentRequestCancellation.Cancel();
                IsBusy = false;

                // 添加取消消息
                var cancelMessage = new ChatMessage
                {
                    Role = "assistant",
                    SenderName = "AI助手",
                    Content = "⏹️ 请求已被用户取消。",
                    Timestamp = DateTime.Now,
                    TopicId = SelectedTopic?.Id ?? 0,
                    Avatar = "pack://application:,,,/uni-ai.png"
                };

                if (SelectedTopic != null)
                {
                    _ = Task.Run(async () =>
                    {
                        await _topicService.AddMessageAsync(cancelMessage);
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            Messages.Add(cancelMessage);
                        });
                    });
                }
            }
        }

        private async Task LoadTopics()
        {
            var topics = await _topicService.GetTopicsAsync(_username);
            Topics.Clear();
            foreach (var topic in topics)
            {
                Topics.Add(topic);
            }
            if (Topics.Any())
            {
                SelectedTopic = Topics.First();
            }
        }

        private async void LoadMessagesForTopic()
        {
            if (SelectedTopic == null)
            {
                Messages.Clear();
                return;
            }

            Messages.Clear();
            var messages = await _topicService.GetMessagesByTopicAsync(SelectedTopic.Id);
            foreach (var message in messages)
            {
                message.SenderName = message.Role == "user" ? message.SenderName : "AI助手";
                message.Avatar = message.Role == "user" ? _avatar : "pack://application:,,,/uni-ai.png";

                // 解析消息中的图片URL
                message.ImageUrls = ExtractImageUrls(message.Content);

                Messages.Add(message);
            }

            // 异步设置对话历史，不阻塞UI
            _ = Task.Run(async () =>
            {
                var semanticKernelService = await GetSemanticKernelServiceAsync();
                semanticKernelService.SetConversationHistory(messages);
            });
        }

        private async Task SendMessage()
        {
            if (string.IsNullOrWhiteSpace(UserInput) || SelectedTopic == null)
                return;

            var userMessageContent = UserInput;

            // 检查是否是新话题的第一条消息，如果是则自动生成话题标题
            var isFirstMessage = Messages.Count == 0 && SelectedTopic.Name.StartsWith("新话题");

            var userMessage = new ChatMessage
            {
                Role = "user",
                SenderName = _username,
                Content = userMessageContent,
                Timestamp = DateTime.Now,
                TopicId = SelectedTopic.Id,
                Avatar = _avatar,
            };

            // 解析用户消息中的图片URL
            userMessage.ImageUrls = ExtractImageUrls(userMessageContent);

            // 调试输出
            System.Diagnostics.Debug.WriteLine($"用户消息内容: {userMessageContent}");
            System.Diagnostics.Debug.WriteLine($"提取到的图片URL数量: {userMessage.ImageUrls.Count}");
            foreach (var url in userMessage.ImageUrls)
            {
                System.Diagnostics.Debug.WriteLine($"  图片URL: {url}");
            }

            await _topicService.AddMessageAsync(userMessage);
            Messages.Add(userMessage);
            UserInput = string.Empty;

            IsBusy = true;

            // 如果是第一条消息，生成话题标题
            if (isFirstMessage)
            {
                try
                {
                    var semanticKernelService = await GetSemanticKernelServiceAsync();
                    var topicTitle = await semanticKernelService.GenerateTopicTitleAsync(userMessageContent);
                    SelectedTopic.Name = topicTitle;
                    await _topicService.UpdateTopicAsync(SelectedTopic);
                }
                catch (Exception)
                {
                    // 如果生成标题失败，使用用户消息的前15个字符作为标题
                    var fallbackTitle = userMessageContent.Length > 15 ? userMessageContent.Substring(0, 15) + "..." : userMessageContent;
                    SelectedTopic.Name = fallbackTitle;
                    await _topicService.UpdateTopicAsync(SelectedTopic);
                }
            }

            await GenerateAIResponse(userMessageContent);

            IsBusy = false;
        }

        private async Task GenerateAIResponse(string userMessage)
        {
            try
            {
                // 创建新的取消令牌源
                _currentRequestCancellation = new CancellationTokenSource();

                var semanticKernelService = await GetSemanticKernelServiceAsync();
                var aiResponseContent = await semanticKernelService.GetChatResponseAsync(userMessage, _currentRequestCancellation.Token);

                var aiMessage = new ChatMessage
                {
                    Role = "assistant",
                    SenderName = "AI助手",
                    Content = aiResponseContent,
                    Timestamp = DateTime.Now,
                    TopicId = SelectedTopic.Id,
                    Avatar = "pack://application:,,,/uni-ai.png"
                };

                // 解析AI响应中的图片URL
                aiMessage.ImageUrls = ExtractImageUrls(aiResponseContent);

                // 调试输出
                System.Diagnostics.Debug.WriteLine($"AI响应内容: {aiResponseContent}");
                System.Diagnostics.Debug.WriteLine($"提取到的图片URL数量: {aiMessage.ImageUrls.Count}");
                foreach (var url in aiMessage.ImageUrls)
                {
                    System.Diagnostics.Debug.WriteLine($"  图片URL: {url}");
                }

                await _topicService.AddMessageAsync(aiMessage);
                Messages.Add(aiMessage);

                // 更新使用统计
                await LoadUsageStatistics();
            }
            catch (TimeoutException ex)
            {
                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    SenderName = "AI助手",
                    Content = $"⚠️ {ex.Message}\n\n请检查网络连接或稍后重试。",
                    Timestamp = DateTime.Now,
                    TopicId = SelectedTopic.Id,
                    Avatar = "pack://application:,,,/uni-ai.png"
                };
                await _topicService.AddMessageAsync(errorMessage);
                Messages.Add(errorMessage);
            }
            catch (Exception ex)
            {
                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    SenderName = "AI助手",
                    Content = $"❌ 抱歉，处理您的请求时出现了错误：{ex.Message}\n\n请稍后重试。",
                    Timestamp = DateTime.Now,
                    TopicId = SelectedTopic.Id,
                    Avatar = "pack://application:,,,/uni-ai.png"
                };
                await _topicService.AddMessageAsync(errorMessage);
                Messages.Add(errorMessage);
            }
        }

        private async Task CreateNewTopic()
        {
            var newTopic = await _topicService.CreateTopicAsync("新话题 " + DateTime.Now.ToString("HH:mm:ss"), _username);
            Topics.Insert(0, newTopic);
            SelectedTopic = newTopic;
        }

        private async Task DeleteTopic(Topic topic)
        {
            if (topic == null) return;

            // 显示确认对话框
            var result = MessageBox.Show(
                $"确定要删除会话 \"{topic.Name}\" 吗？\n\n此操作将删除该会话中的所有消息，且无法撤销。",
                "确认删除会话",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning,
                MessageBoxResult.No);

            if (result == MessageBoxResult.Yes)
            {
                await _topicService.DeleteTopicAsync(topic);
                Topics.Remove(topic);

                if (SelectedTopic == topic)
                {
                    SelectedTopic = Topics.FirstOrDefault();
                }
            }
        }

        private async Task EditTopic(Topic topic)
        {
            if (topic == null) return;

            // 使用输入框来获取新的话题名称
            var newName = Microsoft.VisualBasic.Interaction.InputBox("编辑话题名称:", "编辑话题", topic.Name);
            if (!string.IsNullOrEmpty(newName) && newName != topic.Name)
            {
                topic.Name = newName;
                await _topicService.UpdateTopicAsync(topic);
                // 由于Topic实现了INotifyPropertyChanged，UI会自动更新
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 从消息内容中提取图片URL
        /// 支持多种格式：Markdown图片语法、HTML img标签、直接URL等
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>图片URL列表</returns>
        private List<string> ExtractImageUrls(string content)
        {
            var imageUrls = new List<string>();

            if (string.IsNullOrWhiteSpace(content))
                return imageUrls;

            try
            {
                // 1. Markdown图片语法: ![alt](url) - 支持HTTP/HTTPS URL和本地路径
                var markdownPattern = @"!\[.*?\]\(([^\s\)]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s\)]*)?)\)";
                var markdownMatches = System.Text.RegularExpressions.Regex.Matches(content, markdownPattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase |
                    System.Text.RegularExpressions.RegexOptions.Multiline);

                foreach (System.Text.RegularExpressions.Match match in markdownMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    var processedUrl = ProcessImagePath(url);
                    if (!string.IsNullOrWhiteSpace(processedUrl) && !imageUrls.Contains(processedUrl))
                    {
                        imageUrls.Add(processedUrl);
                    }
                }

                // 2. HTML img标签: <img src="url" /> - 支持HTTP/HTTPS URL和本地路径
                var htmlPattern = @"<img[^>]+src=[""']?([^\s""'>]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s""'>]*)?)[""']?[^>]*>";
                var htmlMatches = System.Text.RegularExpressions.Regex.Matches(content, htmlPattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase |
                    System.Text.RegularExpressions.RegexOptions.Multiline);

                foreach (System.Text.RegularExpressions.Match match in htmlMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    var processedUrl = ProcessImagePath(url);
                    if (!string.IsNullOrWhiteSpace(processedUrl) && !imageUrls.Contains(processedUrl))
                    {
                        imageUrls.Add(processedUrl);
                    }
                }

                // 3. 直接的图片URL (独立一行或被空格包围) - 支持HTTP/HTTPS URL和本地路径，包括带引号的路径
                var directUrlPattern = @"(?:^|\s)(?:[""']?)([^\s""']+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s""']*)?)(?:[""']?)(?:\s|$)";
                var directMatches = System.Text.RegularExpressions.Regex.Matches(content.Replace('`', ' '), directUrlPattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase |
                    System.Text.RegularExpressions.RegexOptions.Multiline);

                foreach (System.Text.RegularExpressions.Match match in directMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    // 过滤掉明显不是图片路径的匹配（如单词中包含图片扩展名的情况）
                    var processedUrl = ProcessImagePath(url);
                    if (!string.IsNullOrWhiteSpace(processedUrl) && !imageUrls.Contains(processedUrl) && IsValidImagePath(processedUrl))
                    {
                        imageUrls.Add(processedUrl);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序运行
                System.Diagnostics.Debug.WriteLine($"ExtractImageUrls error: {ex.Message}");
            }

            return imageUrls;
        }

        /// <summary>
        /// 处理图片路径，移除引号并补全相对路径
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>处理后的路径</returns>
        private string ProcessImagePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return path;

            // 移除前后的引号（单引号或双引号）
            path = path.Trim();
            if ((path.StartsWith("\"") && path.EndsWith("\"")) ||
                (path.StartsWith("'") && path.EndsWith("'")))
            {
                path = path.Substring(1, path.Length - 2);
            }

            // 如果是HTTP/HTTPS URL，直接返回
            if (path.StartsWith("http://", StringComparison.OrdinalIgnoreCase) ||
                path.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
                return path;

            // 如果是绝对路径，直接返回
            if (System.IO.Path.IsPathRooted(path))
                return path;

            // 对于相对路径和简单文件名，补全为当前工作目录的绝对路径
            try
            {
                var _baseUploadDirectory = System.IO.Path.Combine(AppContext.BaseDirectory, "uploads");
                var viewModel = ViewModelLocator.MainViewModelInstance; // Use shared instance
                var userDirectory = System.IO.Path.Combine(_baseUploadDirectory, viewModel.Username);
                var fullPath = System.IO.Path.Combine(userDirectory, path.Replace("_","-"));
                return fullPath;
            }
            catch (Exception ex)
            {
                // 如果路径无效，记录错误并返回原路径
                System.Diagnostics.Debug.WriteLine($"ProcessImagePath error for '{path}': {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 验证是否为有效的图片路径
        /// </summary>
        /// <param name="path">路径字符串</param>
        /// <returns>是否为有效图片路径</returns>
        private bool IsValidImagePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return false;

            try
            {
                // HTTP/HTTPS URL
                if (path.StartsWith("http://", StringComparison.OrdinalIgnoreCase) ||
                    path.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
                    return true;

                // 本地路径验证
                if (System.IO.Path.IsPathRooted(path))
                {
                    // 检查是否为有效的文件路径格式
                    var fileName = System.IO.Path.GetFileName(path);
                    if (string.IsNullOrWhiteSpace(fileName))
                        return false;

                    // 检查文件扩展名
                    var extension = System.IO.Path.GetExtension(fileName).ToLowerInvariant();
                    var validExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg" };
                    if (!validExtensions.Contains(extension))
                        return false;

                    // 检查路径中是否包含无效字符
                    var invalidChars = System.IO.Path.GetInvalidPathChars();
                    if (path.Any(c => invalidChars.Contains(c)))
                        return false;

                    return true;
                }

                // 如果不是绝对路径也不是URL，可能是处理失败的情况
                return false;
            }
            catch (Exception ex)
            {
                // 路径格式无效
                System.Diagnostics.Debug.WriteLine($"IsValidImagePath error for '{path}': {ex.Message}");
                return false;
            }
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Func<Task> _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Func<Task> execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }
        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = () => { execute(); return Task.CompletedTask; };
            _canExecute = canExecute;
        }
        public bool CanExecute(object parameter) => _canExecute == null || _canExecute();

        public async void Execute(object parameter) => await _execute();

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
    public class RelayCommand<T> : ICommand
    {
        private readonly Func<T, Task> _execute;
        private readonly Predicate<T> _canExecute;

        public RelayCommand(Func<T, Task> execute, Predicate<T> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }
        public RelayCommand(Action<T> execute, Predicate<T> canExecute = null)
        {
            _execute = (T param) => { execute(param); return Task.CompletedTask; };
            _canExecute = canExecute;
        }
        public bool CanExecute(object parameter) => _canExecute == null || _canExecute((T)parameter);

        public async void Execute(object parameter) => await _execute((T)parameter);

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
