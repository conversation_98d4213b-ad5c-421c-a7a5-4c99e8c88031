using System;
using System.Threading.Tasks;
using Yidev.LocalAI.Services;
using Yidev.LocalAI.Models;
using Serilog;

namespace Yidev.LocalAI.Test
{
    /// <summary>
    /// 使用次数跟踪功能测试
    /// </summary>
    public class TestUsageTracking
    {
        public static async Task Main(string[] args)
        {
            // 配置日志
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .CreateLogger();

            Console.WriteLine("=== 使用次数跟踪功能测试 ===");
            Console.WriteLine();

            var usageTrackingService = new UsageTrackingService();
            var testUsername = "test_user_" + DateTime.Now.Ticks;

            try
            {
                // 测试1: 获取初始使用统计
                Console.WriteLine("1. 测试获取初始使用统计");
                var initialStats = await usageTrackingService.GetUserUsageStatisticsAsync(testUsername);
                Console.WriteLine($"   用户: {initialStats.Username}");
                Console.WriteLine($"   聊天使用: {initialStats.ChatUsage}/{initialStats.ChatLimit}");
                Console.WriteLine($"   标题生成使用: {initialStats.TitleGenerationUsage}/{initialStats.TitleGenerationLimit}");
                Console.WriteLine($"   限制启用: {initialStats.IsLimitEnabled}");
                Console.WriteLine();

                // 测试2: 记录使用次数
                Console.WriteLine("2. 测试记录使用次数");
                for (int i = 1; i <= 5; i++)
                {
                    var chatResult = await usageTrackingService.RecordUsageAsync(testUsername, UsageTypes.ChatCompletion, i);
                    var titleResult = await usageTrackingService.RecordUsageAsync(testUsername, UsageTypes.TopicTitleGeneration, i);
                    Console.WriteLine($"   记录第{i}次使用 - 聊天: {chatResult}, 标题: {titleResult}");
                }
                Console.WriteLine();

                // 测试3: 检查使用限制
                Console.WriteLine("3. 测试使用限制检查");
                var chatLimitCheck = await usageTrackingService.CheckUsageLimitAsync(testUsername, UsageTypes.ChatCompletion);
                var titleLimitCheck = await usageTrackingService.CheckUsageLimitAsync(testUsername, UsageTypes.TopicTitleGeneration);
                
                Console.WriteLine($"   聊天限制检查:");
                Console.WriteLine($"     允许: {chatLimitCheck.IsAllowed}");
                Console.WriteLine($"     当前使用: {chatLimitCheck.CurrentUsage}");
                Console.WriteLine($"     限制: {chatLimitCheck.Limit}");
                Console.WriteLine($"     剩余: {chatLimitCheck.RemainingUsage}");
                Console.WriteLine($"     消息: {chatLimitCheck.Message}");
                
                Console.WriteLine($"   标题生成限制检查:");
                Console.WriteLine($"     允许: {titleLimitCheck.IsAllowed}");
                Console.WriteLine($"     当前使用: {titleLimitCheck.CurrentUsage}");
                Console.WriteLine($"     限制: {titleLimitCheck.Limit}");
                Console.WriteLine($"     剩余: {titleLimitCheck.RemainingUsage}");
                Console.WriteLine($"     消息: {titleLimitCheck.Message}");
                Console.WriteLine();

                // 测试4: 获取更新后的使用统计
                Console.WriteLine("4. 测试获取更新后的使用统计");
                var updatedStats = await usageTrackingService.GetUserUsageStatisticsAsync(testUsername);
                Console.WriteLine($"   聊天使用: {updatedStats.ChatUsage}/{updatedStats.ChatLimit}");
                Console.WriteLine($"   标题生成使用: {updatedStats.TitleGenerationUsage}/{updatedStats.TitleGenerationLimit}");
                Console.WriteLine($"   剩余聊天次数: {updatedStats.RemainingChatUsage}");
                Console.WriteLine($"   剩余标题生成次数: {updatedStats.RemainingTitleGenerationUsage}");
                Console.WriteLine();

                // 测试5: 更新用户限制配置
                Console.WriteLine("5. 测试更新用户限制配置");
                var updateResult = await usageTrackingService.UpdateUserLimitConfigAsync(testUsername, 10, 5, true);
                Console.WriteLine($"   更新结果: {updateResult}");
                
                var configUpdatedStats = await usageTrackingService.GetUserUsageStatisticsAsync(testUsername);
                Console.WriteLine($"   更新后聊天限制: {configUpdatedStats.ChatLimit}");
                Console.WriteLine($"   更新后标题生成限制: {configUpdatedStats.TitleGenerationLimit}");
                Console.WriteLine();

                // 测试6: 测试超出限制的情况
                Console.WriteLine("6. 测试超出限制的情况");
                // 记录更多使用次数直到超出限制
                for (int i = 6; i <= 15; i++)
                {
                    await usageTrackingService.RecordUsageAsync(testUsername, UsageTypes.ChatCompletion, i);
                }
                
                var limitExceededCheck = await usageTrackingService.CheckUsageLimitAsync(testUsername, UsageTypes.ChatCompletion);
                Console.WriteLine($"   超限检查 - 允许: {limitExceededCheck.IsAllowed}");
                Console.WriteLine($"   消息: {limitExceededCheck.Message}");
                Console.WriteLine();

                // 测试7: 禁用限制
                Console.WriteLine("7. 测试禁用限制");
                await usageTrackingService.UpdateUserLimitConfigAsync(testUsername, 10, 5, false);
                var disabledLimitCheck = await usageTrackingService.CheckUsageLimitAsync(testUsername, UsageTypes.ChatCompletion);
                Console.WriteLine($"   禁用限制后 - 允许: {disabledLimitCheck.IsAllowed}");
                Console.WriteLine($"   消息: {disabledLimitCheck.Message}");
                Console.WriteLine();

                Console.WriteLine("=== 所有测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
