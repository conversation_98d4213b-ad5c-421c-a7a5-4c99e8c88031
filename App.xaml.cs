﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Diagnostics;
using System.Windows;
using Yidev.LocalAI.Models;
using Yidev.LocalAI.Services;
using Yidev.LocalAI.Services.Mcp;

namespace Yidev.LocalAI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost _host;
        private readonly Stopwatch _startupStopwatch = new Stopwatch();
        private SplashWindow _splashWindow;

        protected override async void OnStartup(StartupEventArgs e)
        {
            _startupStopwatch.Start();
            base.OnStartup(e);

            // 显示启动画面
            _splashWindow = new SplashWindow();
            _splashWindow.Show();
            _splashWindow.StartProgress();

            Log.Information("应用程序启动开始...");
            _splashWindow.UpdateStatus("正在初始化应用程序...");

            var hostBuildStart = Stopwatch.StartNew();

            _host = Host.CreateDefaultBuilder()
                .UseSerilog((context, configuration) =>
                {
                    // The original logger config is moved here
                    configuration
                        .MinimumLevel.Debug()
                        .WriteTo.Debug()
                        .WriteTo.Console()
                        .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day);
                })
                .ConfigureServices((context, services) =>
                {
                    // Register Core Services with lazy initialization
                    services.AddSingleton<TopicService>();

                    // 使用 Lazy<T> 延迟初始化 SemanticKernelService
                    services.AddSingleton<Lazy<SemanticKernelService>>(provider =>
                        new Lazy<SemanticKernelService>(() =>
                            SemanticKernelService.CreateAsync().GetAwaiter().GetResult()));

                    // Register ViewModels
                    services.AddSingleton<MainViewModel>();

                    // Register Windows
                    services.AddSingleton<MainWindow>();

                    // 插件现在直接在 SemanticKernelService 中创建，无需在 DI 容器中注册
                    // services.AddSingleton<Yidev.LocalAI.McpPlugins.FileReaderPlugin>();
                    // services.AddSingleton<Yidev.LocalAI.McpPlugins.FileWriterPlugin>();

                    // --- MCP Service Loading ---
                    // 1. Load services from the McpServices directory (延迟加载)
                    services.AddMcpServices();

                    // 2. For demonstration, we also register the sample service directly.
                    // This ensures the service is available without requiring a separate DLL.
                    // 显式注册示例服务已不再需要，由 AddMcpServices 自动扫描并（按设置）完成注册。
                })
                .Build();

            hostBuildStart.Stop();
            Log.Information("依赖注入容器构建完成，耗时: {ElapsedMs}ms", hostBuildStart.ElapsedMilliseconds);
            _splashWindow.UpdateStatus("正在加载配置...");
            _splashWindow.SetProgress(25);

            var hostStartTime = Stopwatch.StartNew();
            await _host.StartAsync();
            hostStartTime.Stop();
            Log.Information("Host 启动完成，耗时: {ElapsedMs}ms", hostStartTime.ElapsedMilliseconds);
            _splashWindow.UpdateStatus("正在连接服务...");
            _splashWindow.SetProgress(50);

            var windowCreateTime = Stopwatch.StartNew();
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            windowCreateTime.Stop();
            Log.Information("主窗口创建完成，耗时: {ElapsedMs}ms", windowCreateTime.ElapsedMilliseconds);
            _splashWindow.UpdateStatus("正在准备用户界面...");
            _splashWindow.SetProgress(75);

            var windowShowTime = Stopwatch.StartNew();
            mainWindow.Show();
            windowShowTime.Stop();
            Log.Information("主窗口显示完成，耗时: {ElapsedMs}ms", windowShowTime.ElapsedMilliseconds);
            _splashWindow.UpdateStatus("即将完成...");
            _splashWindow.SetProgress(100);

            _startupStopwatch.Stop();
            Log.Information("应用程序启动完成，总耗时: {ElapsedMs}ms", _startupStopwatch.ElapsedMilliseconds);

            // 完成启动画面并关闭
            _splashWindow.CompleteProgress();
            await _splashWindow.CloseWithFadeOut();
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            Log.Information("Application Shutting Down");

            if (_host != null)
            {
                using (_host)
                {
                    await _host.StopAsync(TimeSpan.FromSeconds(5));
                }
            }

            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
