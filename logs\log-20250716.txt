2025-07-16 09:54:16.852 +08:00 [DBG] Hosting starting
2025-07-16 09:54:16.913 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 09:54:16.919 +08:00 [INF] Hosting environment: Production
2025-07-16 09:54:16.921 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-16 09:54:16.922 +08:00 [DBG] Hosting started
2025-07-16 09:54:16.924 +08:00 [INF] Application Starting Up
2025-07-16 09:54:17.946 +08:00 [DBG] warn: 2025/7/16 09:54:17.945 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-16 09:54:18.113 +08:00 [DBG] info: 2025/7/16 09:54:18.113 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-16 09:54:18.119 +08:00 [INF] TopicService initialized and database ensured.
2025-07-16 09:54:18.292 +08:00 [INF] Initializing SemanticKernelService...
2025-07-16 09:54:18.311 +08:00 [INF] SemanticKernelService initialized.
2025-07-16 09:54:21.666 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-16 09:54:25.611 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-16 09:54:25.632 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-16 09:54:25.637 +08:00 [INF] Getting topics for user: llk
2025-07-16 09:54:26.164 +08:00 [DBG] info: 2025/7/16 09:54:26.164 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-16 09:54:26.223 +08:00 [INF] Getting messages for topic ID: 1
2025-07-16 09:54:26.303 +08:00 [DBG] info: 2025/7/16 09:54:26.303 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-16 09:54:26.321 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-16 09:54:26.322 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-16 09:54:43.242 +08:00 [INF] Application Shutting Down
2025-07-16 09:54:43.245 +08:00 [DBG] Hosting stopping
2025-07-16 09:54:43.247 +08:00 [INF] Application is shutting down...
2025-07-16 09:54:43.248 +08:00 [DBG] Hosting stopped
2025-07-16 10:15:30.841 +08:00 [DBG] Hosting starting
2025-07-16 10:15:30.903 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 10:15:30.910 +08:00 [INF] Hosting environment: Production
2025-07-16 10:15:30.912 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-16 10:15:30.913 +08:00 [DBG] Hosting started
2025-07-16 10:15:30.914 +08:00 [INF] Application Starting Up
2025-07-16 10:15:31.892 +08:00 [DBG] warn: 2025/7/16 10:15:31.892 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-16 10:15:32.052 +08:00 [DBG] info: 2025/7/16 10:15:32.052 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-16 10:15:32.057 +08:00 [INF] TopicService initialized and database ensured.
2025-07-16 10:15:32.230 +08:00 [INF] Initializing SemanticKernelService...
2025-07-16 10:15:32.248 +08:00 [INF] SemanticKernelService initialized.
2025-07-16 10:15:35.727 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-16 10:15:40.280 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-16 10:15:40.300 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-16 10:15:40.305 +08:00 [INF] Getting topics for user: llk
2025-07-16 10:15:40.831 +08:00 [DBG] info: 2025/7/16 10:15:40.831 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-16 10:15:40.890 +08:00 [INF] Getting messages for topic ID: 1
2025-07-16 10:15:40.970 +08:00 [DBG] info: 2025/7/16 10:15:40.970 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-16 10:15:40.988 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-16 10:15:40.990 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-16 10:16:13.250 +08:00 [INF] Application Shutting Down
2025-07-16 10:16:13.254 +08:00 [DBG] Hosting stopping
2025-07-16 10:16:13.255 +08:00 [INF] Application is shutting down...
2025-07-16 10:16:13.257 +08:00 [DBG] Hosting stopped
2025-07-16 16:52:07.668 +08:00 [INF] 依赖注入容器构建完成，耗时: 133ms
2025-07-16 16:52:07.727 +08:00 [DBG] Hosting starting
2025-07-16 16:52:07.744 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 16:52:07.750 +08:00 [INF] Hosting environment: Production
2025-07-16 16:52:07.751 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-16 16:52:07.752 +08:00 [DBG] Hosting started
2025-07-16 16:52:07.753 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-16 16:52:07.940 +08:00 [INF] TopicService initialized.
2025-07-16 16:52:07.943 +08:00 [INF] 主窗口创建完成，耗时: 188ms
2025-07-16 16:52:08.605 +08:00 [DBG] warn: 2025/7/16 16:52:08.605 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-16 16:52:08.766 +08:00 [DBG] info: 2025/7/16 16:52:08.766 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-16 16:52:08.772 +08:00 [INF] Database ensured and initialized.
2025-07-16 16:52:08.776 +08:00 [INF] Getting topics for user: llk
2025-07-16 16:52:09.312 +08:00 [DBG] info: 2025/7/16 16:52:09.312 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-16 16:52:09.376 +08:00 [INF] Getting messages for topic ID: 1
2025-07-16 16:52:09.461 +08:00 [DBG] info: 2025/7/16 16:52:09.461 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-16 16:52:09.489 +08:00 [INF] Initializing SemanticKernelService...
2025-07-16 16:52:09.508 +08:00 [INF] SemanticKernelService initialized.
2025-07-16 16:52:09.511 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-16 16:52:09.654 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-16 16:52:09.657 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-16 16:52:09.658 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-16 16:52:09.764 +08:00 [INF] 主窗口显示完成，耗时: 1820ms
2025-07-16 16:52:09.765 +08:00 [INF] 应用程序启动完成，总耗时: 2653ms
2025-07-16 16:52:13.071 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-16 16:52:13.939 +08:00 [INF] Application Shutting Down
2025-07-16 16:52:13.943 +08:00 [DBG] Hosting stopping
2025-07-16 16:52:13.944 +08:00 [INF] Application is shutting down...
2025-07-16 16:52:13.946 +08:00 [DBG] Hosting stopped
