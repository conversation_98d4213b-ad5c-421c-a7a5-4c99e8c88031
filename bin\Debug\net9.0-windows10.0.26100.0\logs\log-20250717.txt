2025-07-17 15:15:00.821 +08:00 [INF] 依赖注入容器构建完成，耗时: 3270ms
2025-07-17 15:15:00.906 +08:00 [DBG] Hosting starting
2025-07-17 15:15:00.923 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 15:15:00.928 +08:00 [INF] Hosting environment: Production
2025-07-17 15:15:00.929 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 15:15:00.931 +08:00 [DBG] Hosting started
2025-07-17 15:15:00.931 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-17 15:15:01.448 +08:00 [INF] TopicService initialized.
2025-07-17 15:15:01.450 +08:00 [INF] 主窗口创建完成，耗时: 517ms
2025-07-17 15:15:04.413 +08:00 [DBG] warn: 2025/7/17 15:15:04.413 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 15:15:04.591 +08:00 [DBG] info: 2025/7/17 15:15:04.591 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (26ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 15:15:04.597 +08:00 [INF] Database ensured and initialized.
2025-07-17 15:15:04.600 +08:00 [INF] Getting topics for user: llk
2025-07-17 15:15:05.121 +08:00 [DBG] info: 2025/7/17 15:15:05.121 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 15:15:05.184 +08:00 [INF] Getting messages for topic ID: 40
2025-07-17 15:15:05.262 +08:00 [DBG] info: 2025/7/17 15:15:05.262 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='40'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:15:05.751 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 15:15:06.095 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 15:15:06.172 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 15:15:06.783 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 15:15:06.785 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:15:06.786 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 15:15:06.801 +08:00 [INF] 主窗口显示完成，耗时: 5348ms
2025-07-17 15:15:06.804 +08:00 [INF] 应用程序启动完成，总耗时: 9668ms
2025-07-17 15:15:10.064 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 15:15:14.539 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 15:15:20.943 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 15:15:24.617 +08:00 [ERR] 在后台初始化 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
npm error code E404
npm error 404 Not Found - GET https://registry.npmjs.org/mcp-windows-desktop-automation - Not found
npm error 404
npm error 404  'mcp-windows-desktop-automation@*' is not in this registry.
npm error 404
npm error 404 Note that you can also install from a
npm error 404 tarball, folder, http url, or git url.
npm error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-07-17T07_15_21_313Z-debug-0.log
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeMcpPluginsAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 86
2025-07-17 15:15:24.657 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-17 15:25:13.572 +08:00 [INF] Application Shutting Down
2025-07-17 15:25:13.581 +08:00 [DBG] Hosting stopping
2025-07-17 15:25:13.588 +08:00 [INF] Application is shutting down...
2025-07-17 15:25:13.591 +08:00 [DBG] Hosting stopped
2025-07-17 15:25:40.697 +08:00 [INF] 依赖注入容器构建完成，耗时: 131ms
2025-07-17 15:25:40.761 +08:00 [DBG] Hosting starting
2025-07-17 15:25:40.778 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 15:25:40.784 +08:00 [INF] Hosting environment: Production
2025-07-17 15:25:40.785 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 15:25:40.786 +08:00 [DBG] Hosting started
2025-07-17 15:25:40.787 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-17 15:25:40.972 +08:00 [INF] TopicService initialized.
2025-07-17 15:25:40.975 +08:00 [INF] 主窗口创建完成，耗时: 186ms
2025-07-17 15:25:41.655 +08:00 [DBG] warn: 2025/7/17 15:25:41.655 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 15:25:41.815 +08:00 [DBG] info: 2025/7/17 15:25:41.815 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 15:25:41.821 +08:00 [INF] Database ensured and initialized.
2025-07-17 15:25:41.824 +08:00 [INF] Getting topics for user: llk
2025-07-17 15:25:42.361 +08:00 [DBG] info: 2025/7/17 15:25:42.361 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 15:25:42.425 +08:00 [INF] Getting messages for topic ID: 40
2025-07-17 15:25:42.506 +08:00 [DBG] info: 2025/7/17 15:25:42.506 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='40'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:25:42.534 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 15:25:42.554 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 15:25:42.558 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 15:25:42.705 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 15:25:42.707 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:25:42.708 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 15:25:42.917 +08:00 [INF] 主窗口显示完成，耗时: 1940ms
2025-07-17 15:25:42.918 +08:00 [INF] 应用程序启动完成，总耗时: 2746ms
2025-07-17 15:25:47.620 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 15:25:54.655 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 15:25:58.905 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 15:26:46.365 +08:00 [INF] Application Shutting Down
2025-07-17 15:26:46.369 +08:00 [DBG] Hosting stopping
2025-07-17 15:26:46.371 +08:00 [INF] Application is shutting down...
2025-07-17 15:26:46.373 +08:00 [DBG] Hosting stopped
2025-07-17 15:27:34.496 +08:00 [INF] 依赖注入容器构建完成，耗时: 136ms
2025-07-17 15:27:34.558 +08:00 [DBG] Hosting starting
2025-07-17 15:27:34.576 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 15:27:34.581 +08:00 [INF] Hosting environment: Production
2025-07-17 15:27:34.582 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 15:27:34.583 +08:00 [DBG] Hosting started
2025-07-17 15:27:34.584 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-17 15:27:34.766 +08:00 [INF] TopicService initialized.
2025-07-17 15:27:34.769 +08:00 [INF] 主窗口创建完成，耗时: 183ms
2025-07-17 15:27:35.432 +08:00 [DBG] warn: 2025/7/17 15:27:35.431 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 15:27:35.590 +08:00 [DBG] info: 2025/7/17 15:27:35.590 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 15:27:35.595 +08:00 [INF] Database ensured and initialized.
2025-07-17 15:27:35.599 +08:00 [INF] Getting topics for user: llk
2025-07-17 15:27:36.121 +08:00 [DBG] info: 2025/7/17 15:27:36.121 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 15:27:36.183 +08:00 [INF] Getting messages for topic ID: 40
2025-07-17 15:27:36.262 +08:00 [DBG] info: 2025/7/17 15:27:36.262 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='40'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:27:36.289 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 15:27:36.309 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 15:27:36.312 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 15:27:36.460 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 15:27:36.462 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:27:36.463 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 15:27:36.675 +08:00 [INF] 主窗口显示完成，耗时: 1905ms
2025-07-17 15:27:36.677 +08:00 [INF] 应用程序启动完成，总耗时: 2714ms
2025-07-17 15:27:39.805 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 15:27:43.858 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 15:27:47.501 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 15:27:50.432 +08:00 [ERR] 在后台初始化 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
npm error code E404
npm error 404 Not Found - GET https://registry.npmjs.org/mcp-windows-desktop-automation - Not found
npm error 404
npm error 404  'mcp-windows-desktop-automation@0.1.0' is not in this registry.
npm error 404
npm error 404 Note that you can also install from a
npm error 404 tarball, folder, http url, or git url.
npm error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-07-17T07_27_47_881Z-debug-0.log
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeMcpPluginsAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 86
2025-07-17 15:27:50.467 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-17 15:30:25.106 +08:00 [INF] 依赖注入容器构建完成，耗时: 134ms
2025-07-17 15:30:25.170 +08:00 [DBG] Hosting starting
2025-07-17 15:30:25.188 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 15:30:25.194 +08:00 [INF] Hosting environment: Production
2025-07-17 15:30:25.195 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 15:30:25.196 +08:00 [DBG] Hosting started
2025-07-17 15:30:25.197 +08:00 [INF] Host 启动完成，耗时: 36ms
2025-07-17 15:30:25.383 +08:00 [INF] TopicService initialized.
2025-07-17 15:30:25.386 +08:00 [INF] 主窗口创建完成，耗时: 186ms
2025-07-17 15:30:26.055 +08:00 [DBG] warn: 2025/7/17 15:30:26.055 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 15:30:26.214 +08:00 [DBG] info: 2025/7/17 15:30:26.214 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 15:30:26.219 +08:00 [INF] Database ensured and initialized.
2025-07-17 15:30:26.223 +08:00 [INF] Getting topics for user: llk
2025-07-17 15:30:26.745 +08:00 [DBG] info: 2025/7/17 15:30:26.745 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 15:30:26.808 +08:00 [INF] Getting messages for topic ID: 40
2025-07-17 15:30:26.887 +08:00 [DBG] info: 2025/7/17 15:30:26.887 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='40'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:30:26.914 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 15:30:26.935 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 15:30:26.938 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 15:30:27.082 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 15:30:27.084 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:30:27.085 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 15:30:27.303 +08:00 [INF] 主窗口显示完成，耗时: 1915ms
2025-07-17 15:30:27.305 +08:00 [INF] 应用程序启动完成，总耗时: 2743ms
2025-07-17 15:30:30.431 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 15:30:34.503 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 15:30:38.624 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 15:30:38.939 +08:00 [INF] MCP windows 插件已添加: windows
2025-07-17 15:30:38.940 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 15:31:04.199 +08:00 [INF] Creating topic '新话题 15:31:04' for user: llk
2025-07-17 15:31:04.300 +08:00 [DBG] info: 2025/7/17 15:31:04.300 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-17T15:31:04.2020335+08:00' (DbType = DateTime), @p1='新话题 15:31:04' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-17 15:31:04.427 +08:00 [INF] Topic '新话题 15:31:04' created with ID: 41
2025-07-17 15:31:04.432 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 15:31:04.437 +08:00 [DBG] info: 2025/7/17 15:31:04.437 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:31:04.439 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:31:04.440 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-17 15:31:38.387 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:31:38.400 +08:00 [DBG] info: 2025/7/17 15:31:38.400 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='显示南网eLink窗口' (Nullable = false) (Size = 11), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:31:38.3865945+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:31:38.424 +08:00 [INF] Generating topic title for user message: 显示南网eLink窗口
2025-07-17 15:32:08.439 +08:00 [WRN] Topic title generation timed out after 30 seconds
2025-07-17 15:32:08.442 +08:00 [INF] Updating topic ID: 41
2025-07-17 15:32:08.452 +08:00 [DBG] info: 2025/7/17 15:32:08.452 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='41', @p0='2025-07-17T15:31:04.2020335+08:00' (DbType = DateTime), @p1='显示南网eLink窗口' (Nullable = false) (Size = 11), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-17 15:32:08.460 +08:00 [INF] Getting chat response for user message: 显示南网eLink窗口
2025-07-17 15:32:08.485 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 15:32:08.487 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 15:32:13.199 +08:00 [INF] Received chat response: 
2025-07-17 15:32:13.201 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:32:13.204 +08:00 [DBG] info: 2025/7/17 15:32:13.204 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='' (Nullable = false), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:32:13.2018524+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:33:46.657 +08:00 [INF] Updating message ID: 201
2025-07-17 15:33:46.662 +08:00 [DBG] info: 2025/7/17 15:33:46.662 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p5='201', @p0='显示当前桌面软件 南网eLink 激活窗口' (Nullable = false) (Size = 21), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:31:38.3865945+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      UPDATE "ChatMessages" SET "Content" = @p0, "Role" = @p1, "SenderName" = @p2, "Timestamp" = @p3, "TopicId" = @p4
      WHERE "Id" = @p5
      RETURNING 1;
2025-07-17 15:33:46.680 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 15:33:46.682 +08:00 [DBG] info: 2025/7/17 15:33:46.682 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:33:46.685 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:33:46.687 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 15:33:49.844 +08:00 [INF] Deleting message ID: 202
2025-07-17 15:33:49.851 +08:00 [DBG] info: 2025/7/17 15:33:49.851 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='202'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 15:33:49.859 +08:00 [INF] Getting chat response for user message: 显示当前桌面软件 南网eLink 激活窗口
2025-07-17 15:33:54.648 +08:00 [INF] Received chat response: 
2025-07-17 15:33:54.650 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:33:54.653 +08:00 [DBG] info: 2025/7/17 15:33:54.653 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='' (Nullable = false), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:33:54.6504424+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:34:37.038 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:34:37.041 +08:00 [DBG] info: 2025/7/17 15:34:37.041 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='窗口截图保存' (Nullable = false) (Size = 6), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:34:37.0387888+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:34:37.045 +08:00 [INF] Getting chat response for user message: 窗口截图保存
2025-07-17 15:34:50.773 +08:00 [INF] Received chat response: 好的，已为您激活“南网eLink”窗口。

请问您想将截图保存到哪里，文件名是什么？
2025-07-17 15:34:50.775 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:34:50.778 +08:00 [DBG] info: 2025/7/17 15:34:50.778 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，已为您激活“南网eLink”窗口。

请问您想将截图保存到哪里，文件名是什么？' (Nullable = false) (Size = 42), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:34:50.7757181+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:36:11.962 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:36:11.965 +08:00 [DBG] info: 2025/7/17 15:36:11.965 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='保存到 C:\Users\<USER>\Downloads\elink.png 并显示' (Nullable = false) (Size = 50), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:36:11.9620646+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:36:11.969 +08:00 [INF] Getting chat response for user message: 保存到 C:\Users\<USER>\Downloads\elink.png 并显示
2025-07-17 15:36:31.631 +08:00 [INF] Received chat response: 抱歉，我无法将截图直接保存到 `C:\Users\<USER>\Downloads\`，因为我只能在我的工作目录 `'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk'` 中创建文件。

不过，我可以为您将 "南网eLink" 窗口的截图保存到我的工作目录中，文件名为 `elink.png`。然后您可以从那里移动它。可以吗？
2025-07-17 15:36:31.633 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:36:31.636 +08:00 [DBG] info: 2025/7/17 15:36:31.636 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='抱歉，我无法将截图直接保存到 `C:\Users\<USER>\Downloads\`，因为我只能在我的工作目录 `'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk'` 中创建文件。

不过，我可以为您将 "南网eLink" 窗口的截图保存到我的工作目录中，文件名为 `elink.png`。然后您可以从那里移动它。可以吗？' (Nullable = false) (Size = 224), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:36:31.6337719+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:37:21.586 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:37:21.590 +08:00 [DBG] info: 2025/7/17 15:37:21.590 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='可以' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:37:21.5867946+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:37:21.593 +08:00 [INF] Getting chat response for user message: 可以
2025-07-17 15:37:46.251 +08:00 [INF] Received chat response: 好的，截图已保存到 `D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/elink.png`。

<img src="D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/elink.png" alt="南网eLink 截图" />
2025-07-17 15:37:46.254 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:37:46.256 +08:00 [DBG] info: 2025/7/17 15:37:46.256 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，截图已保存到 `D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/elink.png`。

<img src="D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/elink.png" alt="南网eLink 截图" />' (Nullable = false) (Size = 222), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:37:46.2539873+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:39:16.833 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:39:16.836 +08:00 [DBG] info: 2025/7/17 15:39:16.836 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='eLink 电费工作群中有什么消息？' (Nullable = false) (Size = 18), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:39:16.8330590+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:39:16.841 +08:00 [INF] Getting chat response for user message: eLink 电费工作群中有什么消息？
2025-07-17 15:39:24.968 +08:00 [INF] Received chat response: 我无法直接访问“eLink 电费工作群”中的消息。我没有与该应用程序的内部功能进行交互的能力，因此无法读取聊天记录。

我可以帮您操作“南网eLink”的窗口，例如激活它或截图，但我无法理解或提取其中的特定内容。
2025-07-17 15:39:24.970 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:39:24.973 +08:00 [DBG] info: 2025/7/17 15:39:24.973 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我无法直接访问“eLink 电费工作群”中的消息。我没有与该应用程序的内部功能进行交互的能力，因此无法读取聊天记录。

我可以帮您操作“南网eLink”的窗口，例如激活它或截图，但我无法理解或提取其中的特定内容。' (Nullable = false) (Size = 106), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:39:24.9707170+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:45:23.435 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:45:23.438 +08:00 [DBG] info: 2025/7/17 15:45:23.438 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@p0='保存截图到当前工作目录' (Nullable = false) (Size = 11), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:45:23.4349444+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:45:23.454 +08:00 [INF] Getting chat response for user message: 保存截图到当前工作目录
2025-07-17 15:45:30.237 +08:00 [INF] Received chat response: 截图已保存到 `D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/screenshot.png`。

<img src="D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/screenshot.png" alt="截图" />
2025-07-17 15:45:30.239 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:45:30.242 +08:00 [DBG] info: 2025/7/17 15:45:30.242 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='截图已保存到 `D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/screenshot.png`。

<img src="D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/screenshot.png" alt="截图" />' (Nullable = false) (Size = 221), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:45:30.2392968+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:45:48.677 +08:00 [INF] Application Shutting Down
2025-07-17 15:45:48.682 +08:00 [DBG] Hosting stopping
2025-07-17 15:45:48.684 +08:00 [INF] Application is shutting down...
2025-07-17 15:45:48.689 +08:00 [DBG] Hosting stopped
2025-07-17 15:46:35.944 +08:00 [INF] 依赖注入容器构建完成，耗时: 132ms
2025-07-17 15:46:36.008 +08:00 [DBG] Hosting starting
2025-07-17 15:46:36.025 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 15:46:36.030 +08:00 [INF] Hosting environment: Production
2025-07-17 15:46:36.032 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 15:46:36.033 +08:00 [DBG] Hosting started
2025-07-17 15:46:36.034 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-17 15:46:36.214 +08:00 [INF] TopicService initialized.
2025-07-17 15:46:36.216 +08:00 [INF] 主窗口创建完成，耗时: 181ms
2025-07-17 15:46:36.876 +08:00 [DBG] warn: 2025/7/17 15:46:36.875 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 15:46:37.035 +08:00 [DBG] info: 2025/7/17 15:46:37.035 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 15:46:37.041 +08:00 [INF] Database ensured and initialized.
2025-07-17 15:46:37.044 +08:00 [INF] Getting topics for user: llk
2025-07-17 15:46:37.578 +08:00 [DBG] info: 2025/7/17 15:46:37.578 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 15:46:37.644 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 15:46:37.725 +08:00 [DBG] info: 2025/7/17 15:46:37.725 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 15:46:37.758 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 15:46:37.781 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 15:46:37.785 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 15:46:37.935 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 15:46:37.938 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 15:46:37.939 +08:00 [INF] Setting conversation history. Message count: 12
2025-07-17 15:46:38.327 +08:00 [INF] 主窗口显示完成，耗时: 2109ms
2025-07-17 15:46:38.329 +08:00 [INF] 应用程序启动完成，总耗时: 2930ms
2025-07-17 15:46:41.069 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 15:46:45.911 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 15:46:46.218 +08:00 [INF] MCP windows 插件已添加: windows
2025-07-17 15:46:46.219 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 15:46:53.107 +08:00 [INF] Deleting message ID: 213
2025-07-17 15:46:53.208 +08:00 [DBG] info: 2025/7/17 15:46:53.208 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='213'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 15:46:53.226 +08:00 [INF] Getting chat response for user message: 保存截图到当前工作目录
2025-07-17 15:46:53.251 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 15:46:53.252 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 15:46:56.669 +08:00 [INF] Received chat response: 我没有截图的功能。我只能访问和操作我的工作目录中的文件和目录。
2025-07-17 15:46:56.672 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:46:56.691 +08:00 [DBG] info: 2025/7/17 15:46:56.691 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我没有截图的功能。我只能访问和操作我的工作目录中的文件和目录。' (Nullable = false) (Size = 31), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:46:56.6715166+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:48:36.877 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:48:36.880 +08:00 [DBG] info: 2025/7/17 15:48:36.880 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='激活“南网eLink”的窗口' (Nullable = false) (Size = 14), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:48:36.8769600+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:48:36.884 +08:00 [INF] Getting chat response for user message: 激活“南网eLink”的窗口
2025-07-17 15:48:48.526 +08:00 [INF] Received chat response: 好的，已为您激活“南网eLink”窗口。
2025-07-17 15:48:48.528 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:48:48.531 +08:00 [DBG] info: 2025/7/17 15:48:48.531 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，已为您激活“南网eLink”窗口。' (Nullable = false) (Size = 20), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:48:48.5284208+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:48:58.890 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:48:58.893 +08:00 [DBG] info: 2025/7/17 15:48:58.893 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='并截图' (Nullable = false) (Size = 3), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:48:58.8904830+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:48:58.897 +08:00 [INF] Getting chat response for user message: 并截图
2025-07-17 15:49:02.692 +08:00 [INF] Received chat response: 我没有截图的功能。我只能访问和操作我的工作目录中的文件和目录。
2025-07-17 15:49:02.693 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:49:02.696 +08:00 [DBG] info: 2025/7/17 15:49:02.696 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我没有截图的功能。我只能访问和操作我的工作目录中的文件和目录。' (Nullable = false) (Size = 31), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:49:02.6936286+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:49:53.815 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:49:53.818 +08:00 [DBG] info: 2025/7/17 15:49:53.818 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='点击 “南网eLink”窗口中的 “电费工作群”' (Nullable = false) (Size = 24), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:49:53.8155722+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:49:53.823 +08:00 [INF] Getting chat response for user message: 点击 “南网eLink”窗口中的 “电费工作群”
2025-07-17 15:50:11.377 +08:00 [INF] Received chat response: 抱歉，我无法直接点击“电费工作群”。这可能是因为“南网eLink”没有使用标准的窗口控件，所以我无法识别它。

您可以将鼠标移动到“电费工作群”上，然后告诉我，我再执行点击操作。
2025-07-17 15:50:11.379 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:50:11.384 +08:00 [DBG] info: 2025/7/17 15:50:11.384 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='抱歉，我无法直接点击“电费工作群”。这可能是因为“南网eLink”没有使用标准的窗口控件，所以我无法识别它。

您可以将鼠标移动到“电费工作群”上，然后告诉我，我再执行点击操作。' (Nullable = false) (Size = 89), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:50:11.3796341+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:52:19.176 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:52:19.179 +08:00 [DBG] info: 2025/7/17 15:52:19.179 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='屏幕截图' (Nullable = false) (Size = 4), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T15:52:19.1766640+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 15:52:19.234 +08:00 [INF] Getting chat response for user message: 屏幕截图
2025-07-17 15:52:23.144 +08:00 [INF] Received chat response: 我没有截图的功能。我只能访问和操作我的工作目录中的文件和目录。
2025-07-17 15:52:23.145 +08:00 [INF] Adding message to topic ID: 41
2025-07-17 15:52:23.148 +08:00 [DBG] info: 2025/7/17 15:52:23.148 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我没有截图的功能。我只能访问和操作我的工作目录中的文件和目录。' (Nullable = false) (Size = 31), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T15:52:23.1457430+08:00' (DbType = DateTime), @p4='41'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:04:49.408 +08:00 [INF] 依赖注入容器构建完成，耗时: 134ms
2025-07-17 16:04:49.473 +08:00 [DBG] Hosting starting
2025-07-17 16:04:49.490 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:04:49.495 +08:00 [INF] Hosting environment: Production
2025-07-17 16:04:49.497 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:04:49.498 +08:00 [DBG] Hosting started
2025-07-17 16:04:49.498 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-17 16:04:49.679 +08:00 [INF] TopicService initialized.
2025-07-17 16:04:49.681 +08:00 [INF] 主窗口创建完成，耗时: 181ms
2025-07-17 16:04:50.360 +08:00 [DBG] warn: 2025/7/17 16:04:50.360 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:04:50.523 +08:00 [DBG] info: 2025/7/17 16:04:50.523 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:04:50.528 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:04:50.532 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:04:51.061 +08:00 [DBG] info: 2025/7/17 16:04:51.061 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:04:51.121 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 16:04:51.199 +08:00 [DBG] info: 2025/7/17 16:04:51.199 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:04:51.229 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:04:51.250 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:04:51.253 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:04:51.394 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:04:51.396 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:04:51.397 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-17 16:04:51.801 +08:00 [INF] 主窗口显示完成，耗时: 2118ms
2025-07-17 16:04:51.802 +08:00 [INF] 应用程序启动完成，总耗时: 2929ms
2025-07-17 16:04:55.078 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:05:00.904 +08:00 [INF] Creating topic '新话题 16:05:00' for user: llk
2025-07-17 16:05:01.016 +08:00 [DBG] info: 2025/7/17 16:05:01.016 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-17T16:05:00.9068709+08:00' (DbType = DateTime), @p1='新话题 16:05:00' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-17 16:05:01.045 +08:00 [INF] Topic '新话题 16:05:00' created with ID: 42
2025-07-17 16:05:01.110 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:05:01.115 +08:00 [DBG] info: 2025/7/17 16:05:01.115 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:05:01.118 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:05:01.119 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-17 16:05:01.427 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:05:01.607 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:05:01.609 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:05:30.169 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:05:30.183 +08:00 [DBG] info: 2025/7/17 16:05:30.183 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='打开百度并截图保存到当前工作目录' (Nullable = false) (Size = 16), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T16:05:30.1682103+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:05:30.193 +08:00 [INF] Generating topic title for user message: 打开百度并截图保存到当前工作目录
2025-07-17 16:05:42.595 +08:00 [INF] Generated topic title: 百度截图并保存
2025-07-17 16:05:42.598 +08:00 [INF] Updating topic ID: 42
2025-07-17 16:05:42.604 +08:00 [DBG] info: 2025/7/17 16:05:42.604 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='42', @p0='2025-07-17T16:05:00.9068709+08:00' (DbType = DateTime), @p1='百度截图并保存' (Nullable = false) (Size = 7), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-17 16:05:42.612 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存到当前工作目录
2025-07-17 16:05:42.635 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 16:05:42.637 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 16:06:16.618 +08:00 [INF] Received chat response: 我已成功打开百度并为您截图，文件已保存至 'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/baidu_screenshot.png'。
2025-07-17 16:06:16.621 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:06:16.624 +08:00 [DBG] info: 2025/7/17 16:06:16.623 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我已成功打开百度并为您截图，文件已保存至 'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/baidu_screenshot.png'。' (Nullable = false) (Size = 123), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:06:16.6210732+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:22:29.709 +08:00 [INF] Application Shutting Down
2025-07-17 16:22:29.713 +08:00 [DBG] Hosting stopping
2025-07-17 16:22:29.718 +08:00 [INF] Application is shutting down...
2025-07-17 16:22:29.723 +08:00 [DBG] Hosting stopped
2025-07-17 16:23:12.764 +08:00 [INF] 依赖注入容器构建完成，耗时: 132ms
2025-07-17 16:23:12.833 +08:00 [DBG] Hosting starting
2025-07-17 16:23:12.852 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:23:12.859 +08:00 [INF] Hosting environment: Production
2025-07-17 16:23:12.860 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:23:12.862 +08:00 [DBG] Hosting started
2025-07-17 16:23:12.863 +08:00 [INF] Host 启动完成，耗时: 39ms
2025-07-17 16:23:13.051 +08:00 [INF] TopicService initialized.
2025-07-17 16:23:13.053 +08:00 [INF] 主窗口创建完成，耗时: 188ms
2025-07-17 16:23:13.723 +08:00 [DBG] warn: 2025/7/17 16:23:13.723 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:23:13.887 +08:00 [DBG] info: 2025/7/17 16:23:13.887 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:23:13.892 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:23:13.896 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:23:14.428 +08:00 [DBG] info: 2025/7/17 16:23:14.428 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:23:14.492 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:23:14.574 +08:00 [DBG] info: 2025/7/17 16:23:14.573 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:23:14.602 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:23:14.624 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:23:14.627 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:23:14.775 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:23:14.777 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:23:14.779 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:23:14.953 +08:00 [INF] 主窗口显示完成，耗时: 1898ms
2025-07-17 16:23:14.955 +08:00 [INF] 应用程序启动完成，总耗时: 2739ms
2025-07-17 16:23:18.159 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:23:24.599 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:23:24.717 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:23:24.719 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:23:33.553 +08:00 [INF] Deleting message ID: 224
2025-07-17 16:23:33.658 +08:00 [DBG] info: 2025/7/17 16:23:33.658 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='224'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:23:33.675 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存到当前工作目录
2025-07-17 16:23:33.701 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 16:23:33.702 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 16:25:33.710 +08:00 [WRN] Chat response request timed out after 2 minutes
2025-07-17 16:25:33.713 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:25:33.734 +08:00 [DBG] info: 2025/7/17 16:25:33.734 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='⚠️ AI服务响应超时，请稍后重试

请检查网络连接或稍后重试。' (Nullable = false) (Size = 32), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:25:33.7126657+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:30:33.456 +08:00 [INF] Deleting message ID: 225
2025-07-17 16:30:33.460 +08:00 [DBG] info: 2025/7/17 16:30:33.460 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@p0='225'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:30:33.467 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存到当前工作目录
2025-07-17 16:31:08.709 +08:00 [INF] Received chat response: 我已成功打开百度并为您截图，文件已保存至 'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/baidu_screenshot.png'。
2025-07-17 16:31:08.712 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:31:08.715 +08:00 [DBG] info: 2025/7/17 16:31:08.715 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我已成功打开百度并为您截图，文件已保存至 'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/baidu_screenshot.png'。' (Nullable = false) (Size = 123), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:31:08.7121056+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:39:39.265 +08:00 [INF] Updating message ID: 223
2025-07-17 16:39:39.272 +08:00 [DBG] info: 2025/7/17 16:39:39.272 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@p5='223', @p0='打开百度并截图保存' (Nullable = false) (Size = 9), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T16:05:30.1682103' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      UPDATE "ChatMessages" SET "Content" = @p0, "Role" = @p1, "SenderName" = @p2, "Timestamp" = @p3, "TopicId" = @p4
      WHERE "Id" = @p5
      RETURNING 1;
2025-07-17 16:39:39.386 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:39:39.391 +08:00 [DBG] info: 2025/7/17 16:39:39.391 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:39:39.394 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:39:39.395 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:39:42.193 +08:00 [INF] Deleting message ID: 226
2025-07-17 16:39:42.196 +08:00 [DBG] info: 2025/7/17 16:39:42.196 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='226'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:39:42.239 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存
2025-07-17 16:40:02.592 +08:00 [INF] Received chat response: 我无法打开百度或必应。由于技术问题，我现在无法访问任何网页。因此，我无法完成您的请求。
2025-07-17 16:40:02.595 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:40:02.598 +08:00 [DBG] info: 2025/7/17 16:40:02.598 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我无法打开百度或必应。由于技术问题，我现在无法访问任何网页。因此，我无法完成您的请求。' (Nullable = false) (Size = 43), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:40:02.5953524+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:40:05.359 +08:00 [INF] Application Shutting Down
2025-07-17 16:40:05.364 +08:00 [DBG] Hosting stopping
2025-07-17 16:40:05.366 +08:00 [INF] Application is shutting down...
2025-07-17 16:40:05.368 +08:00 [DBG] Hosting stopped
2025-07-17 16:40:09.341 +08:00 [INF] 依赖注入容器构建完成，耗时: 133ms
2025-07-17 16:40:09.403 +08:00 [DBG] Hosting starting
2025-07-17 16:40:09.420 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:40:09.426 +08:00 [INF] Hosting environment: Production
2025-07-17 16:40:09.427 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:40:09.428 +08:00 [DBG] Hosting started
2025-07-17 16:40:09.428 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-17 16:40:09.611 +08:00 [INF] TopicService initialized.
2025-07-17 16:40:09.613 +08:00 [INF] 主窗口创建完成，耗时: 183ms
2025-07-17 16:40:10.260 +08:00 [DBG] warn: 2025/7/17 16:40:10.260 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:40:10.418 +08:00 [DBG] info: 2025/7/17 16:40:10.418 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:40:10.423 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:40:10.426 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:40:10.942 +08:00 [DBG] info: 2025/7/17 16:40:10.942 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:40:11.003 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:40:11.085 +08:00 [DBG] info: 2025/7/17 16:40:11.085 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:40:11.113 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:40:11.134 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:40:11.137 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:40:11.278 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:40:11.281 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:40:11.282 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:40:11.472 +08:00 [INF] 主窗口显示完成，耗时: 1858ms
2025-07-17 16:40:11.474 +08:00 [INF] 应用程序启动完成，总耗时: 2669ms
2025-07-17 16:40:14.638 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:40:19.127 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:40:19.235 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:40:19.236 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:40:27.287 +08:00 [INF] Deleting message ID: 227
2025-07-17 16:40:27.393 +08:00 [DBG] info: 2025/7/17 16:40:27.393 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='227'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:40:27.412 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存
2025-07-17 16:40:27.437 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 16:40:27.439 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 16:42:27.441 +08:00 [WRN] Chat response request timed out after 2 minutes
2025-07-17 16:42:27.443 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:42:27.464 +08:00 [DBG] info: 2025/7/17 16:42:27.464 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='⚠️ AI服务响应超时，请稍后重试

请检查网络连接或稍后重试。' (Nullable = false) (Size = 32), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:42:27.4429963+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:46:08.739 +08:00 [INF] Deleting message ID: 228
2025-07-17 16:46:08.743 +08:00 [DBG] info: 2025/7/17 16:46:08.743 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@p0='228'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:46:08.749 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存
2025-07-17 16:46:13.719 +08:00 [INF] Received chat response: 我无法打开百度或必应。由于技术问题，我现在无法访问任何网页。因此，我无法完成您的请求。
2025-07-17 16:46:13.721 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:46:13.724 +08:00 [DBG] info: 2025/7/17 16:46:13.724 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我无法打开百度或必应。由于技术问题，我现在无法访问任何网页。因此，我无法完成您的请求。' (Nullable = false) (Size = 43), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:46:13.7213780+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:46:23.788 +08:00 [INF] Deleting message ID: 229
2025-07-17 16:46:23.791 +08:00 [DBG] info: 2025/7/17 16:46:23.791 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='229'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:46:23.805 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存
2025-07-17 16:46:28.272 +08:00 [INF] Received chat response: 我无法打开百度或必应。由于技术问题，我现在无法访问任何网页。因此，我无法完成您的请求。
2025-07-17 16:46:28.274 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:46:28.278 +08:00 [DBG] info: 2025/7/17 16:46:28.278 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我无法打开百度或必应。由于技术问题，我现在无法访问任何网页。因此，我无法完成您的请求。' (Nullable = false) (Size = 43), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:46:28.2747452+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:46:40.044 +08:00 [INF] Application Shutting Down
2025-07-17 16:46:40.048 +08:00 [DBG] Hosting stopping
2025-07-17 16:46:40.053 +08:00 [INF] Application is shutting down...
2025-07-17 16:46:40.056 +08:00 [DBG] Hosting stopped
2025-07-17 16:46:43.275 +08:00 [INF] 依赖注入容器构建完成，耗时: 134ms
2025-07-17 16:46:43.337 +08:00 [DBG] Hosting starting
2025-07-17 16:46:43.354 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:46:43.360 +08:00 [INF] Hosting environment: Production
2025-07-17 16:46:43.362 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:46:43.363 +08:00 [DBG] Hosting started
2025-07-17 16:46:43.365 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-17 16:46:43.548 +08:00 [INF] TopicService initialized.
2025-07-17 16:46:43.550 +08:00 [INF] 主窗口创建完成，耗时: 183ms
2025-07-17 16:46:44.212 +08:00 [DBG] warn: 2025/7/17 16:46:44.212 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:46:44.375 +08:00 [DBG] info: 2025/7/17 16:46:44.375 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:46:44.381 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:46:44.385 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:46:44.930 +08:00 [DBG] info: 2025/7/17 16:46:44.930 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:46:44.994 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:46:45.074 +08:00 [DBG] info: 2025/7/17 16:46:45.074 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:46:45.102 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:46:45.124 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:46:45.127 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:46:45.274 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:46:45.276 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:46:45.278 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:46:45.501 +08:00 [INF] 主窗口显示完成，耗时: 1949ms
2025-07-17 16:46:45.503 +08:00 [INF] 应用程序启动完成，总耗时: 2778ms
2025-07-17 16:46:48.702 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:46:52.982 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:46:53.064 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:46:53.066 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:47:00.548 +08:00 [INF] Deleting message ID: 230
2025-07-17 16:47:00.647 +08:00 [DBG] info: 2025/7/17 16:47:00.647 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='230'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-17 16:47:00.665 +08:00 [INF] Getting chat response for user message: 打开百度并截图保存
2025-07-17 16:47:00.690 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 16:47:00.692 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 16:48:00.152 +08:00 [INF] Received chat response: 好的，已为您打开百度并截图，文件已保存至 'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/baidu_screenshot.png'。
2025-07-17 16:48:00.155 +08:00 [INF] Adding message to topic ID: 42
2025-07-17 16:48:00.174 +08:00 [DBG] info: 2025/7/17 16:48:00.174 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，已为您打开百度并截图，文件已保存至 'D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk/baidu_screenshot.png'。' (Nullable = false) (Size = 123), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T16:48:00.1547125+08:00' (DbType = DateTime), @p4='42'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 16:49:07.429 +08:00 [INF] Application Shutting Down
2025-07-17 16:49:07.433 +08:00 [DBG] Hosting stopping
2025-07-17 16:49:07.435 +08:00 [INF] Application is shutting down...
2025-07-17 16:49:07.443 +08:00 [DBG] Hosting stopped
2025-07-17 16:49:32.820 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-17 16:49:32.881 +08:00 [DBG] Hosting starting
2025-07-17 16:49:32.898 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:49:32.904 +08:00 [INF] Hosting environment: Production
2025-07-17 16:49:32.905 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:49:32.906 +08:00 [DBG] Hosting started
2025-07-17 16:49:32.907 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-17 16:49:33.084 +08:00 [INF] TopicService initialized.
2025-07-17 16:49:33.086 +08:00 [INF] 主窗口创建完成，耗时: 177ms
2025-07-17 16:49:33.742 +08:00 [DBG] warn: 2025/7/17 16:49:33.742 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:49:33.909 +08:00 [DBG] info: 2025/7/17 16:49:33.909 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (20ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:49:33.914 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:49:33.918 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:49:34.438 +08:00 [DBG] info: 2025/7/17 16:49:34.438 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:49:34.500 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:49:34.581 +08:00 [DBG] info: 2025/7/17 16:49:34.581 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:49:34.609 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:49:34.629 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:49:34.632 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:49:34.772 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:49:34.775 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:49:34.776 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:49:35.011 +08:00 [INF] 主窗口显示完成，耗时: 1924ms
2025-07-17 16:49:35.013 +08:00 [INF] 应用程序启动完成，总耗时: 2716ms
2025-07-17 16:49:39.554 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:49:45.168 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:49:45.247 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:49:45.248 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:50:59.208 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 16:50:59.216 +08:00 [DBG] info: 2025/7/17 16:50:59.216 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:50:59.231 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:50:59.235 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-17 16:51:01.744 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:51:01.746 +08:00 [DBG] info: 2025/7/17 16:51:01.746 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:51:01.749 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:51:01.750 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:51:25.448 +08:00 [INF] Application Shutting Down
2025-07-17 16:51:25.452 +08:00 [DBG] Hosting stopping
2025-07-17 16:51:25.455 +08:00 [INF] Application is shutting down...
2025-07-17 16:51:25.459 +08:00 [DBG] Hosting stopped
2025-07-17 16:51:30.179 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-17 16:51:30.241 +08:00 [DBG] Hosting starting
2025-07-17 16:51:30.258 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:51:30.264 +08:00 [INF] Hosting environment: Production
2025-07-17 16:51:30.265 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:51:30.266 +08:00 [DBG] Hosting started
2025-07-17 16:51:30.267 +08:00 [INF] Host 启动完成，耗时: 34ms
2025-07-17 16:51:30.450 +08:00 [INF] TopicService initialized.
2025-07-17 16:51:30.452 +08:00 [INF] 主窗口创建完成，耗时: 182ms
2025-07-17 16:51:31.117 +08:00 [DBG] warn: 2025/7/17 16:51:31.116 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:51:31.275 +08:00 [DBG] info: 2025/7/17 16:51:31.275 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:51:31.281 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:51:31.285 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:51:31.809 +08:00 [DBG] info: 2025/7/17 16:51:31.809 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:51:31.872 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:51:31.955 +08:00 [DBG] info: 2025/7/17 16:51:31.955 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:51:31.989 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:51:32.011 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:51:32.014 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:51:32.162 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:51:32.165 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:51:32.166 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:51:32.394 +08:00 [INF] 主窗口显示完成，耗时: 1940ms
2025-07-17 16:51:32.396 +08:00 [INF] 应用程序启动完成，总耗时: 2759ms
2025-07-17 16:51:38.058 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:51:42.230 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:51:42.311 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:51:42.313 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:53:41.919 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 16:53:41.926 +08:00 [DBG] info: 2025/7/17 16:53:41.926 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:53:41.932 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:53:41.933 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-17 16:53:43.844 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:53:43.847 +08:00 [DBG] info: 2025/7/17 16:53:43.847 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:53:43.850 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:53:43.851 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:58:29.080 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 16:58:29.083 +08:00 [DBG] info: 2025/7/17 16:58:29.083 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:58:29.087 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:58:29.088 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-17 16:58:31.280 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:58:31.283 +08:00 [DBG] info: 2025/7/17 16:58:31.283 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:58:31.286 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:58:31.287 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:58:59.473 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 16:58:59.476 +08:00 [DBG] info: 2025/7/17 16:58:59.476 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:58:59.479 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:58:59.480 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-17 16:59:00.865 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:59:00.868 +08:00 [DBG] info: 2025/7/17 16:59:00.868 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:59:00.870 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:59:00.871 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:59:07.994 +08:00 [INF] Application Shutting Down
2025-07-17 16:59:07.999 +08:00 [DBG] Hosting stopping
2025-07-17 16:59:08.009 +08:00 [INF] Application is shutting down...
2025-07-17 16:59:08.011 +08:00 [DBG] Hosting stopped
2025-07-17 16:59:15.166 +08:00 [INF] 依赖注入容器构建完成，耗时: 128ms
2025-07-17 16:59:15.228 +08:00 [DBG] Hosting starting
2025-07-17 16:59:15.246 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:59:15.252 +08:00 [INF] Hosting environment: Production
2025-07-17 16:59:15.253 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:59:15.254 +08:00 [DBG] Hosting started
2025-07-17 16:59:15.255 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-17 16:59:15.436 +08:00 [INF] TopicService initialized.
2025-07-17 16:59:15.439 +08:00 [INF] 主窗口创建完成，耗时: 181ms
2025-07-17 16:59:16.101 +08:00 [DBG] warn: 2025/7/17 16:59:16.101 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:59:16.258 +08:00 [DBG] info: 2025/7/17 16:59:16.258 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:59:16.264 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:59:16.268 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:59:16.794 +08:00 [DBG] info: 2025/7/17 16:59:16.794 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:59:16.856 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:59:16.936 +08:00 [DBG] info: 2025/7/17 16:59:16.936 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:59:16.965 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:59:16.986 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:59:16.989 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:59:17.138 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:59:17.141 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:59:17.142 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:59:17.357 +08:00 [INF] 主窗口显示完成，耗时: 1917ms
2025-07-17 16:59:17.359 +08:00 [INF] 应用程序启动完成，总耗时: 2719ms
2025-07-17 16:59:21.482 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 16:59:23.239 +08:00 [INF] Getting messages for topic ID: 41
2025-07-17 16:59:23.246 +08:00 [DBG] info: 2025/7/17 16:59:23.246 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='41'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:59:23.251 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:59:23.253 +08:00 [INF] Setting conversation history. Message count: 20
2025-07-17 16:59:25.263 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:59:25.266 +08:00 [DBG] info: 2025/7/17 16:59:25.266 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:59:25.268 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:59:25.270 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:59:25.673 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 16:59:25.788 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 16:59:25.789 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 16:59:32.348 +08:00 [INF] Application Shutting Down
2025-07-17 16:59:32.353 +08:00 [DBG] Hosting stopping
2025-07-17 16:59:32.354 +08:00 [INF] Application is shutting down...
2025-07-17 16:59:32.356 +08:00 [DBG] Hosting stopped
2025-07-17 16:59:53.811 +08:00 [INF] 依赖注入容器构建完成，耗时: 130ms
2025-07-17 16:59:53.874 +08:00 [DBG] Hosting starting
2025-07-17 16:59:53.891 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 16:59:53.897 +08:00 [INF] Hosting environment: Production
2025-07-17 16:59:53.898 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 16:59:53.899 +08:00 [DBG] Hosting started
2025-07-17 16:59:53.899 +08:00 [INF] Host 启动完成，耗时: 33ms
2025-07-17 16:59:54.079 +08:00 [INF] TopicService initialized.
2025-07-17 16:59:54.081 +08:00 [INF] 主窗口创建完成，耗时: 180ms
2025-07-17 16:59:54.724 +08:00 [DBG] warn: 2025/7/17 16:59:54.724 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 16:59:54.889 +08:00 [DBG] info: 2025/7/17 16:59:54.889 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (20ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 16:59:54.895 +08:00 [INF] Database ensured and initialized.
2025-07-17 16:59:54.898 +08:00 [INF] Getting topics for user: llk
2025-07-17 16:59:55.420 +08:00 [DBG] info: 2025/7/17 16:59:55.420 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 16:59:55.490 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 16:59:55.574 +08:00 [DBG] info: 2025/7/17 16:59:55.574 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 16:59:55.602 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 16:59:55.622 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 16:59:55.625 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 16:59:55.767 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 16:59:55.769 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 16:59:55.770 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 16:59:56.031 +08:00 [INF] 主窗口显示完成，耗时: 1947ms
2025-07-17 16:59:56.033 +08:00 [INF] 应用程序启动完成，总耗时: 2755ms
2025-07-17 16:59:59.449 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 17:00:03.968 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 17:00:04.046 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 17:00:04.048 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 17:21:41.802 +08:00 [INF] Creating topic '新话题 17:21:41' for user: llk
2025-07-17 17:21:41.908 +08:00 [DBG] info: 2025/7/17 17:21:41.908 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@p0='2025-07-17T17:21:41.8050019+08:00' (DbType = DateTime), @p1='新话题 17:21:41' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-17 17:21:41.932 +08:00 [INF] Topic '新话题 17:21:41' created with ID: 43
2025-07-17 17:21:41.937 +08:00 [INF] Getting messages for topic ID: 43
2025-07-17 17:21:41.941 +08:00 [DBG] info: 2025/7/17 17:21:41.941 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:21:41.944 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:21:41.945 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-17 17:22:03.547 +08:00 [INF] Adding message to topic ID: 43
2025-07-17 17:22:03.562 +08:00 [DBG] info: 2025/7/17 17:22:03.562 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='搜索 祎开发 并截图保存' (Nullable = false) (Size = 12), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-17T17:22:03.5470220+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 17:22:03.583 +08:00 [INF] Generating topic title for user message: 搜索 祎开发 并截图保存
2025-07-17 17:22:28.744 +08:00 [INF] Generated topic title: 搜索祎开发
2025-07-17 17:22:28.746 +08:00 [INF] Updating topic ID: 43
2025-07-17 17:22:28.752 +08:00 [DBG] info: 2025/7/17 17:22:28.752 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='43', @p0='2025-07-17T17:21:41.8050019+08:00' (DbType = DateTime), @p1='搜索祎开发' (Nullable = false) (Size = 5), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-17 17:22:28.871 +08:00 [INF] Getting chat response for user message: 搜索 祎开发 并截图保存
2025-07-17 17:22:28.896 +08:00 [INF] FileReaderPlugin 已添加到 Kernel
2025-07-17 17:22:28.898 +08:00 [INF] FileWriterPlugin 已添加到 Kernel
2025-07-17 17:22:55.272 +08:00 [INF] Received chat response: 好的，已为您搜索“祎开发”并截图。

不过，搜索结果被 reCAPTCHA（人机验证）拦截了，所以我截取的是当前的验证页面。

图片已保存到您的根目录下的 `search_results.png` 文件中。

需要我尝试点击“我不是机器人”来继续吗？
2025-07-17 17:22:55.275 +08:00 [INF] Adding message to topic ID: 43
2025-07-17 17:22:55.277 +08:00 [DBG] info: 2025/7/17 17:22:55.277 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，已为您搜索“祎开发”并截图。

不过，搜索结果被 reCAPTCHA（人机验证）拦截了，所以我截取的是当前的验证页面。

图片已保存到您的根目录下的 `search_results.png` 文件中。

需要我尝试点击“我不是机器人”来继续吗？' (Nullable = false) (Size = 125), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-17T17:22:55.2750447+08:00' (DbType = DateTime), @p4='43'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-17 17:24:44.759 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 17:24:44.762 +08:00 [DBG] info: 2025/7/17 17:24:44.762 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:24:44.764 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:24:44.765 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:24:46.183 +08:00 [INF] Getting messages for topic ID: 43
2025-07-17 17:24:46.185 +08:00 [DBG] info: 2025/7/17 17:24:46.185 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:24:46.187 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:24:46.188 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:24:48.657 +08:00 [INF] Getting messages for topic ID: 42
2025-07-17 17:24:48.659 +08:00 [DBG] info: 2025/7/17 17:24:48.659 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='42'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:24:48.662 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:24:48.663 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:24:50.007 +08:00 [INF] Getting messages for topic ID: 43
2025-07-17 17:24:50.010 +08:00 [DBG] info: 2025/7/17 17:24:50.010 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:24:50.012 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:24:50.013 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:24:56.196 +08:00 [INF] Application Shutting Down
2025-07-17 17:24:56.199 +08:00 [DBG] Hosting stopping
2025-07-17 17:24:56.201 +08:00 [INF] Application is shutting down...
2025-07-17 17:24:56.212 +08:00 [DBG] Hosting stopped
2025-07-17 17:25:00.642 +08:00 [INF] 依赖注入容器构建完成，耗时: 131ms
2025-07-17 17:25:00.705 +08:00 [DBG] Hosting starting
2025-07-17 17:25:00.723 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 17:25:00.729 +08:00 [INF] Hosting environment: Production
2025-07-17 17:25:00.730 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 17:25:00.731 +08:00 [DBG] Hosting started
2025-07-17 17:25:00.732 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-17 17:25:00.919 +08:00 [INF] TopicService initialized.
2025-07-17 17:25:00.921 +08:00 [INF] 主窗口创建完成，耗时: 186ms
2025-07-17 17:25:01.580 +08:00 [DBG] warn: 2025/7/17 17:25:01.580 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 17:25:01.737 +08:00 [DBG] info: 2025/7/17 17:25:01.737 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 17:25:01.742 +08:00 [INF] Database ensured and initialized.
2025-07-17 17:25:01.746 +08:00 [INF] Getting topics for user: llk
2025-07-17 17:25:02.274 +08:00 [DBG] info: 2025/7/17 17:25:02.274 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 17:25:02.337 +08:00 [INF] Getting messages for topic ID: 43
2025-07-17 17:25:02.453 +08:00 [DBG] info: 2025/7/17 17:25:02.453 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:25:02.482 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 17:25:02.504 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 17:25:02.507 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 17:25:02.654 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 17:25:02.657 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:25:02.658 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:25:02.894 +08:00 [INF] 主窗口显示完成，耗时: 1971ms
2025-07-17 17:25:02.895 +08:00 [INF] 应用程序启动完成，总耗时: 2793ms
2025-07-17 17:25:06.049 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 17:25:09.829 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 17:25:09.913 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 17:25:09.914 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 17:27:02.078 +08:00 [INF] Application Shutting Down
2025-07-17 17:27:02.082 +08:00 [DBG] Hosting stopping
2025-07-17 17:27:02.084 +08:00 [INF] Application is shutting down...
2025-07-17 17:27:02.085 +08:00 [DBG] Hosting stopped
2025-07-17 17:27:17.044 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-17 17:27:17.108 +08:00 [DBG] Hosting starting
2025-07-17 17:27:17.125 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 17:27:17.131 +08:00 [INF] Hosting environment: Production
2025-07-17 17:27:17.133 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 17:27:17.134 +08:00 [DBG] Hosting started
2025-07-17 17:27:17.135 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-17 17:27:17.314 +08:00 [INF] TopicService initialized.
2025-07-17 17:27:17.316 +08:00 [INF] 主窗口创建完成，耗时: 178ms
2025-07-17 17:27:17.968 +08:00 [DBG] warn: 2025/7/17 17:27:17.968 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 17:27:18.132 +08:00 [DBG] info: 2025/7/17 17:27:18.132 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 17:27:18.138 +08:00 [INF] Database ensured and initialized.
2025-07-17 17:27:18.142 +08:00 [INF] Getting topics for user: llk
2025-07-17 17:27:18.688 +08:00 [DBG] info: 2025/7/17 17:27:18.688 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 17:27:18.753 +08:00 [INF] Getting messages for topic ID: 43
2025-07-17 17:27:18.837 +08:00 [DBG] info: 2025/7/17 17:27:18.837 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:27:18.868 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 17:27:18.888 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 17:27:18.892 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 17:27:19.036 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 17:27:19.039 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:27:19.040 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:27:19.281 +08:00 [INF] 主窗口显示完成，耗时: 1963ms
2025-07-17 17:27:19.283 +08:00 [INF] 应用程序启动完成，总耗时: 2758ms
2025-07-17 17:27:22.190 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 17:27:28.798 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 17:27:28.881 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 17:27:28.882 +08:00 [INF] MCP 插件后台初始化完成
2025-07-17 17:29:16.802 +08:00 [INF] Application Shutting Down
2025-07-17 17:29:16.806 +08:00 [DBG] Hosting stopping
2025-07-17 17:29:16.810 +08:00 [INF] Application is shutting down...
2025-07-17 17:29:16.817 +08:00 [DBG] Hosting stopped
2025-07-17 17:29:20.021 +08:00 [INF] 依赖注入容器构建完成，耗时: 129ms
2025-07-17 17:29:20.083 +08:00 [DBG] Hosting starting
2025-07-17 17:29:20.101 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 17:29:20.107 +08:00 [INF] Hosting environment: Production
2025-07-17 17:29:20.108 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-17 17:29:20.110 +08:00 [DBG] Hosting started
2025-07-17 17:29:20.111 +08:00 [INF] Host 启动完成，耗时: 35ms
2025-07-17 17:29:20.294 +08:00 [INF] TopicService initialized.
2025-07-17 17:29:20.297 +08:00 [INF] 主窗口创建完成，耗时: 184ms
2025-07-17 17:29:20.944 +08:00 [DBG] warn: 2025/7/17 17:29:20.944 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-17 17:29:21.102 +08:00 [DBG] info: 2025/7/17 17:29:21.102 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-17 17:29:21.108 +08:00 [INF] Database ensured and initialized.
2025-07-17 17:29:21.112 +08:00 [INF] Getting topics for user: llk
2025-07-17 17:29:21.636 +08:00 [DBG] info: 2025/7/17 17:29:21.636 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-17 17:29:21.698 +08:00 [INF] Getting messages for topic ID: 43
2025-07-17 17:29:21.779 +08:00 [DBG] info: 2025/7/17 17:29:21.779 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='43'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-17 17:29:21.802 +08:00 [ERR] aa search_results.png
2025-07-17 17:29:21.809 +08:00 [INF] Initializing SemanticKernelService...
2025-07-17 17:29:21.829 +08:00 [INF] SemanticKernelService initialized.
2025-07-17 17:29:21.832 +08:00 [INF] 开始后台初始化 MCP 插件...
2025-07-17 17:29:21.978 +08:00 [INF] SemanticKernelService 快速初始化完成，MCP 插件正在后台加载...
2025-07-17 17:29:21.981 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-17 17:29:21.982 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-17 17:29:22.228 +08:00 [INF] 主窗口显示完成，耗时: 1929ms
2025-07-17 17:29:22.230 +08:00 [INF] 应用程序启动完成，总耗时: 2737ms
2025-07-17 17:29:25.515 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-17 17:29:30.055 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-17 17:29:30.133 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-17 17:29:30.135 +08:00 [INF] MCP 插件后台初始化完成
