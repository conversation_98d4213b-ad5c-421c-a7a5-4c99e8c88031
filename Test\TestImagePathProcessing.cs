using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Xunit;
using Yidev.LocalAI.Models;

namespace Yidev.LocalAI.Test
{
    public class TestImagePathProcessing
    {
        private MainViewModel _viewModel;

        public TestImagePathProcessing()
        {
            _viewModel = new MainViewModel();
        }

        [Fact]
        public void TestProcessImagePath_RemovesQuotes()
        {
            // 使用反射访问私有方法
            var method = typeof(MainViewModel).GetMethod("ProcessImagePath", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            // 测试双引号
            var result1 = (string)method.Invoke(_viewModel, new object[] { "\"image.jpg\"" });
            Assert.EndsWith("image.jpg", result1);
            
            // 测试单引号
            var result2 = (string)method.Invoke(_viewModel, new object[] { "'photo.png'" });
            Assert.EndsWith("photo.png", result2);
            
            // 测试无引号
            var result3 = (string)method.Invoke(_viewModel, new object[] { "picture.gif" });
            Assert.EndsWith("picture.gif", result3);
        }

        [Fact]
        public void TestProcessImagePath_ConvertsRelativeToAbsolute()
        {
            var method = typeof(MainViewModel).GetMethod("ProcessImagePath", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            // 测试相对路径
            var result1 = (string)method.Invoke(_viewModel, new object[] { "./images/test.jpg" });
            Assert.True(Path.IsPathRooted(result1));
            Assert.EndsWith("test.jpg", result1);
            
            // 测试简单文件名
            var result2 = (string)method.Invoke(_viewModel, new object[] { "simple.png" });
            Assert.True(Path.IsPathRooted(result2));
            Assert.EndsWith("simple.png", result2);
        }

        [Fact]
        public void TestProcessImagePath_PreservesHttpUrls()
        {
            var method = typeof(MainViewModel).GetMethod("ProcessImagePath", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            var httpUrl = "http://example.com/image.jpg";
            var result1 = (string)method.Invoke(_viewModel, new object[] { httpUrl });
            Assert.Equal(httpUrl, result1);
            
            var httpsUrl = "https://example.com/image.png";
            var result2 = (string)method.Invoke(_viewModel, new object[] { httpsUrl });
            Assert.Equal(httpsUrl, result2);
        }

        [Fact]
        public void TestProcessImagePath_PreservesAbsolutePaths()
        {
            var method = typeof(MainViewModel).GetMethod("ProcessImagePath", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            var absolutePath = @"C:\Users\<USER>\image.jpg";
            var result = (string)method.Invoke(_viewModel, new object[] { absolutePath });
            Assert.Equal(absolutePath, result);
        }

        [Fact]
        public void TestExtractImageUrls_WithQuotes()
        {
            var method = typeof(MainViewModel).GetMethod("ExtractImageUrls", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            // 测试带引号的图片路径
            var content = "这是一张图片 \"image.jpg\" 和另一张 'photo.png'";
            var result = (List<string>)method.Invoke(_viewModel, new object[] { content });
            
            Assert.Contains(result, url => url.EndsWith("image.jpg"));
            Assert.Contains(result, url => url.EndsWith("photo.png"));
            
            // 验证路径已被转换为绝对路径
            Assert.All(result, url => 
            {
                if (!url.StartsWith("http"))
                {
                    Assert.True(Path.IsPathRooted(url));
                }
            });
        }

        [Fact]
        public void TestExtractImageUrls_MixedFormats()
        {
            var method = typeof(MainViewModel).GetMethod("ExtractImageUrls", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            var content = @"
                Markdown: ![alt](./images/test.jpg)
                HTML: <img src=""photo.png"" />
                Direct: picture.gif
                Quoted: ""another.jpg""
                HTTP: https://example.com/web.png
            ";
            
            var result = (List<string>)method.Invoke(_viewModel, new object[] { content });
            
            // 应该提取到所有图片
            Assert.True(result.Count >= 5);
            
            // 验证HTTP URL保持不变
            Assert.Contains("https://example.com/web.png", result);
            
            // 验证本地路径被转换为绝对路径
            var localPaths = result.Where(url => !url.StartsWith("http")).ToList();
            Assert.All(localPaths, path => Assert.True(Path.IsPathRooted(path)));
        }

        [Fact]
        public void TestIsValidImagePath_ValidatesProcessedPaths()
        {
            var method = typeof(MainViewModel).GetMethod("IsValidImagePath", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            // 测试有效的HTTP URL
            var result1 = (bool)method.Invoke(_viewModel, new object[] { "https://example.com/image.jpg" });
            Assert.True(result1);
            
            // 测试有效的绝对路径
            var result2 = (bool)method.Invoke(_viewModel, new object[] { @"C:\Users\<USER>\image.png" });
            Assert.True(result2);
            
            // 测试无效的路径（没有扩展名）
            var result3 = (bool)method.Invoke(_viewModel, new object[] { @"C:\Users\<USER>\notanimage" });
            Assert.False(result3);
            
            // 测试无效的扩展名
            var result4 = (bool)method.Invoke(_viewModel, new object[] { @"C:\Users\<USER>\document.txt" });
            Assert.False(result4);
        }
    }
}
