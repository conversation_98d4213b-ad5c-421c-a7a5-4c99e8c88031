D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-x86\native\WebView2Loader.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-x64\native\WebView2Loader.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-arm64\native\WebView2Loader.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.exe
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.deps.json
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.runtimeconfig.json
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.pdb
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\IdentityModel.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\IdentityModel.OidcClient.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.DependencyInjection.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Options.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Primitives.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Yidev.Agent.Oidc.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Windows.SDK.NET.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\WinRT.Runtime.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Web.WebView2.Core.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Web.WebView2.WinForms.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Web.WebView2.Wpf.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Web.WebView2.Core.xml
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Web.WebView2.WinForms.xml
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Web.WebView2.Wpf.xml
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.csproj.AssemblyReference.cache
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\MainWindow.g.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\App.g.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI_Content.g.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\GeneratedInternalTypeHelper.g.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI_MarkupCompile.cache
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI_MarkupCompile.lref
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\MainWindow.baml
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.g.resources
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.GeneratedMSBuildEditorConfig.editorconfig
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.AssemblyInfoInputs.cache
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.AssemblyInfo.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.csproj.CoreCompileInputs.cache
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.Lo.09356765.Up2Date
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.dll
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\refint\Yidev.LocalAI.dll
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.pdb
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\Yidev.LocalAI.genruntimeconfig.cache
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\ref\Yidev.LocalAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Data.Sqlite.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.EntityFrameworkCore.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.EntityFrameworkCore.Sqlite.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Caching.Memory.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.DependencyModel.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\SQLitePCLRaw.batteries_v2.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\SQLitePCLRaw.core.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\SQLitePCLRaw.provider.e_sqlite3.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Text.Json.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\browser-wasm\nativeassets\net9.0\e_sqlite3.a
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-arm\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-arm64\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-armel\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-mips64\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-musl-s390x\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-s390x\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-x64\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\linux-x86\native\libe_sqlite3.so
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-arm\native\e_sqlite3.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-arm64\native\e_sqlite3.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-x64\native\e_sqlite3.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win-x86\native\e_sqlite3.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Azure.AI.OpenAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Azure.Core.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Bcl.HashCode.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.AI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.AI.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.AI.OpenAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Hosting.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Options.DataAnnotations.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.VectorData.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.SemanticKernel.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.SemanticKernel.Abstractions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.SemanticKernel.Connectors.OpenAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.SemanticKernel.Core.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.SemanticKernel.Plugins.Memory.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\ModelContextProtocol.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\ModelContextProtocol-SemanticKernel.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\ModelContextProtocol.Core.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\OpenAI.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Stef.Validation.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Stef.Validation.Options.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.ClientModel.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Diagnostics.DiagnosticSource.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Memory.Data.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Net.ServerSentEvents.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Numerics.Tensors.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Threading.Channels.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Serilog.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Serilog.Sinks.Debug.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Serilog.Sinks.File.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Serilog.Sinks.Console.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.Binder.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.CommandLine.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.Json.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Configuration.UserSecrets.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Diagnostics.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.FileProviders.Physical.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.FileSystemGlobbing.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Hosting.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.Configuration.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.Console.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.Debug.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.EventLog.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Logging.EventSource.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Serilog.Extensions.Hosting.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Serilog.Extensions.Logging.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\System.Diagnostics.EventLog.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.dll
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\McpSettingsWindow.g.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\McpSettingsWindow.baml
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Markdig.dll
D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0\Markdig.Wpf.dll
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\ImageViewerWindow.baml
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\ImageViewerWindow.g.cs
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\SplashWindow.baml
D:\yidev\Source\yidev.localai\obj\Debug\net9.0-windows10.0.26100.0\SplashWindow.g.cs
